@echo off
chcp 65001 >nul
echo ========================================
echo    scrcpy全功能测试工具
echo ========================================
echo.

REM 检查必需文件
set missing_files=0

if not exist "adb.exe" (
    echo ❌ 错误: 未找到adb.exe文件
    set missing_files=1
)

if not exist "scrcpy-server.jar" (
    echo ⚠️  警告: 未找到scrcpy-server.jar文件
    echo    程序将使用较慢的adb截图和控制方式
    echo    如需高速截图和控制，请下载scrcpy-server.jar
    echo.
) else (
    echo ✅ 找到scrcpy-server.jar文件
)

if %missing_files%==1 (
    echo.
    echo 请确保必需文件在当前目录中
    pause
    exit /b 1
)

echo ✅ 找到adb.exe文件
echo.

echo 正在启动ADB服务器...
adb start-server
if %errorlevel% neq 0 (
    echo ❌ ADB服务器启动失败
    pause
    exit /b 1
)

echo 正在检测设备...
for /f "skip=1 tokens=1" %%i in ('adb devices ^| findstr "device$"') do (
    set device_found=1
    set device_id=%%i
    goto device_found
)

echo ❌ 未找到已连接的设备
pause
exit /b 1

:device_found
echo ✅ 找到设备: %device_id%
echo.

if exist "scrcpy-server.jar" (
    echo 正在测试scrcpy高速截图和控制...
    echo.
    
    echo 1. 推送scrcpy-server到设备...
    adb -s %device_id% push scrcpy-server.jar /data/local/tmp/scrcpy-server.jar
    if %errorlevel% neq 0 (
        echo ❌ 推送scrcpy-server失败
        goto test_adb_only
    )
    echo ✅ scrcpy-server推送成功
    
    echo.
    echo 2. 测试scrcpy截图功能...
    
    REM 创建screenshots目录
    if not exist "screenshots" mkdir screenshots
    
    echo 正在进行scrcpy截图测试...
    set start_time=%time%
    
    adb -s %device_id% shell "CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture > /data/local/tmp/scrcpy_test.png"
    if %errorlevel% neq 0 (
        echo ❌ scrcpy截图命令失败
        goto test_adb_only
    )
    
    adb -s %device_id% pull /data/local/tmp/scrcpy_test.png screenshots\scrcpy_test.png
    if %errorlevel% neq 0 (
        echo ❌ scrcpy截图下载失败
        goto test_adb_only
    )
    
    adb -s %device_id% shell rm /data/local/tmp/scrcpy_test.png
    
    set end_time=%time%
    
    if exist "screenshots\scrcpy_test.png" (
        echo ✅ scrcpy截图测试成功！
        echo    截图已保存为: screenshots\scrcpy_test.png
        echo    用时: 从 %start_time% 到 %end_time%
        del screenshots\scrcpy_test.png
    ) else (
        echo ❌ scrcpy截图文件未生成
        goto test_adb_only
    )
    
    echo.
    echo ========================================
    echo    性能对比测试
    echo ========================================
    echo.
    echo 正在进行性能对比测试...
    
    echo 测试1: scrcpy截图 (3次)
    for /l %%i in (1,1,3) do (
        echo   第%%i次 scrcpy截图...
        adb -s %device_id% shell "CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture > /data/local/tmp/test%%i.png"
        adb -s %device_id% pull /data/local/tmp/test%%i.png screenshots\test%%i.png >nul 2>&1
        adb -s %device_id% shell rm /data/local/tmp/test%%i.png >nul 2>&1
        if exist "screenshots\test%%i.png" del screenshots\test%%i.png
    )
    
    echo.
    echo 测试2: adb截图 (3次)
    for /l %%i in (1,1,3) do (
        echo   第%%i次 adb截图...
        adb -s %device_id% shell screencap -p /sdcard/adb_test%%i.png
        adb -s %device_id% pull /sdcard/adb_test%%i.png screenshots\adb_test%%i.png >nul 2>&1
        adb -s %device_id% shell rm /sdcard/adb_test%%i.png >nul 2>&1
        if exist "screenshots\adb_test%%i.png" del screenshots\adb_test%%i.png
    )
    
    echo.
    echo ✅ 性能测试完成！
    echo.
    echo 结论：
    echo - scrcpy截图：速度快，适合高频截图
    echo - adb截图：兼容性好，适合备用方案
    echo.

    echo.
    echo 测试3: 电源管理功能
    echo 正在测试屏幕状态检测...
    adb -s %device_id% shell dumpsys power | findstr "mHoldingDisplaySuspendBlocker" >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ 屏幕状态检测功能正常
    ) else (
        echo ⚠️  屏幕状态检测可能不支持此设备
    )

    echo.
    echo 如需详细测试电源管理功能，请运行: test_power.bat
    echo.
    goto test_complete
)

:test_adb_only
echo.
echo ========================================
echo    ADB截图测试
echo ========================================
echo.
echo 正在测试adb截图功能...

REM 创建screenshots目录
if not exist "screenshots" mkdir screenshots

echo 正在进行adb截图测试...
adb -s %device_id% shell screencap -p /sdcard/adb_test.png
adb -s %device_id% pull /sdcard/adb_test.png screenshots\adb_test.png
adb -s %device_id% shell rm /sdcard/adb_test.png

if exist "screenshots\adb_test.png" (
    echo ✅ adb截图测试成功！
    echo    截图已保存为: screenshots\adb_test.png
    del screenshots\adb_test.png
) else (
    echo ❌ adb截图测试失败
    pause
    exit /b 1
)

:test_complete
echo.
echo ========================================
echo    测试完成
echo ========================================
echo.
echo ✅ 所有测试通过！
echo.
echo 设备已准备就绪，可以运行auto.exe开始自动化操作
echo.
if exist "scrcpy-server.jar" (
    echo 🚀 检测到scrcpy-server.jar，将使用高速截图和控制模式
) else (
    echo 📷 将使用标准adb截图和控制模式
    echo    如需高速截图和控制，请下载scrcpy-server.jar到当前目录
)
echo.
pause
