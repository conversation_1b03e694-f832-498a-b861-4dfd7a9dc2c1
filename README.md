# 自动化游戏脚本 - scrcpy高速版本

本项目已从极限手游助手的WebSocket连接改为使用原生ADB命令控制Android设备，并集成了scrcpy-server实现高速截图。

## 环境要求

1. **ADB工具**: 需要将 `adb.exe` 文件放置在应用程序的执行目录中
2. **scrcpy-server**: 需要将 `scrcpy-server.jar` 文件放置在应用程序的执行目录中（可选，用于高速截图）
3. **Android设备**: 需要启用USB调试模式
4. **USB连接**: 设备需要通过USB连接到电脑，或者通过网络ADB连接

## 安装步骤

### 1. 获取ADB工具

从以下任一方式获取 `adb.exe`:

- **Android SDK Platform Tools**: 下载官方Android SDK Platform Tools
  - 下载地址: https://developer.android.com/studio/releases/platform-tools
  - 解压后在 `platform-tools` 目录中找到 `adb.exe`

### 2. 获取scrcpy-server.jar

从scrcpy项目获取 `scrcpy-server.jar`:

- **scrcpy官方项目**: https://github.com/Genymobile/scrcpy
- 下载最新版本的scrcpy，在安装目录中找到 `scrcpy-server.jar`
- 或从releases页面直接下载

### 3. 放置文件

将文件复制到应用程序的执行目录中：

目录结构应该如下：
```
应用程序目录/
├── auto.exe
├── adb.exe              # 必需文件
├── scrcpy-server.jar    # 可选文件（用于高速截图）
├── min/                 # 图片模板目录
│   ├── cf.png
│   ├── c4.png
│   └── ...
├── screenshots/         # 截图保存目录（自动创建）
└── threshold.ini        # 置信度配置文件（可选）
```

### 4. 设备准备

1. **启用开发者选项**:
   - 进入设备的"设置" > "关于手机"
   - 连续点击"版本号"7次启用开发者选项

2. **启用USB调试**:
   - 进入"设置" > "开发者选项"
   - 开启"USB调试"

3. **连接设备**:
   - 使用USB线连接设备到电脑
   - 首次连接时设备会弹出授权提示，选择"允许"

## 功能特性

### 主要改进

- ✅ **无需第三方软件**: 不再依赖极限手游助手
- ✅ **原生ADB控制**: 使用Android官方调试工具
- ✅ **高速截图**: 集成scrcpy-server实现毫秒级截图
- ✅ **智能回退**: scrcpy不可用时自动回退到adb截图
- ✅ **更好的兼容性**: 支持所有启用USB调试的Android设备
- ✅ **更稳定的连接**: 避免WebSocket连接问题

### 支持的操作

- **设备检测**: 自动检测已连接的Android设备
- **高速截图**: 优先使用scrcpy-server进行毫秒级截图
- **模拟点击**: 使用 `adb shell input tap` 模拟触摸
- **模拟滑动**: 使用 `adb shell input swipe` 模拟滑动手势
- **分辨率获取**: 使用 `adb shell wm size` 获取屏幕尺寸
- **图像识别**: 使用OpenCV进行模板匹配
- **智能缩放**: 自动将截图缩放到1080x488标准尺寸进行识别

## 使用方法

1. 确保设备已连接并启用USB调试
2. 将 `adb.exe` 和 `scrcpy-server.jar` 放置在应用程序目录
3. 运行 `auto.exe`
4. 程序会自动：
   - 启动ADB服务器
   - 检测连接的设备
   - 初始化scrcpy-server（如果可用）
   - 获取设备分辨率
   - 开始自动化操作

## 性能对比

| 截图方式 | 平均耗时 | 优势 | 劣势 |
|---------|---------|------|------|
| ADB screencap | 800-1500ms | 兼容性好 | 速度慢 |
| scrcpy-server | 50-200ms | 速度快 | 需要额外文件 |

## 故障排除

### 常见问题

1. **"未找到adb.exe文件"**
   - 确保 `adb.exe` 在应用程序目录中

2. **"未找到scrcpy-server.jar文件"**
   - 这是警告信息，程序会自动回退到adb截图
   - 如需高速截图，请下载并放置scrcpy-server.jar

3. **"未找到已连接的设备"**
   - 检查USB连接
   - 确认已启用USB调试
   - 运行 `adb devices` 验证设备状态

4. **scrcpy初始化失败**
   - 程序会自动回退到adb截图方式
   - 检查scrcpy-server.jar文件是否完整
   - 确认设备有足够权限

## 技术说明

### 截图优化策略

1. **优先使用scrcpy-server**: 如果可用，使用scrcpy进行高速截图
2. **智能回退**: scrcpy失败时自动切换到adb截图
3. **标准化处理**: 所有截图都缩放到1080x488进行识别
4. **坐标转换**: 识别结果自动转换回实际设备坐标

### 架构变更

移除的依赖：
- WebSocket连接相关代码
- 极限手游助手相关功能

新增的功能：
- scrcpy-server集成
- 智能截图策略
- 自动回退机制

## 注意事项

- 确保设备屏幕保持亮屏状态
- 避免在自动化过程中手动操作设备
- scrcpy-server.jar文件可选，但强烈推荐使用以获得最佳性能
- 定期清理screenshots目录中的截图文件
