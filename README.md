# 自动化游戏脚本 - ADB版本

本项目已从极限手游助手的WebSocket连接改为使用原生ADB命令控制Android设备。

## 环境要求

1. **ADB工具**: 需要将 `adb.exe` 文件放置在应用程序的执行目录中
2. **Android设备**: 需要启用USB调试模式
3. **USB连接**: 设备需要通过USB连接到电脑，或者通过网络ADB连接

## 安装步骤

### 1. 获取ADB工具

从以下任一方式获取 `adb.exe`:

- **Android SDK Platform Tools**: 下载官方Android SDK Platform Tools
  - 下载地址: https://developer.android.com/studio/releases/platform-tools
  - 解压后在 `platform-tools` 目录中找到 `adb.exe`

- **最小化ADB工具**: 下载独立的ADB工具包
  - 搜索 "minimal adb fastboot" 或类似工具

### 2. 放置ADB文件

将 `adb.exe` 文件复制到应用程序的执行目录中（与 `auto.exe` 同一目录）。

目录结构应该如下：
```
应用程序目录/
├── auto.exe
├── adb.exe          # 必需文件
├── min/             # 图片模板目录
│   ├── cf.png
│   ├── c4.png
│   └── ...
├── screenshots/     # 截图保存目录（自动创建）
└── threshold.ini    # 置信度配置文件（可选）
```

### 3. 设备准备

1. **启用开发者选项**:
   - 进入设备的"设置" > "关于手机"
   - 连续点击"版本号"7次启用开发者选项

2. **启用USB调试**:
   - 进入"设置" > "开发者选项"
   - 开启"USB调试"

3. **连接设备**:
   - 使用USB线连接设备到电脑
   - 首次连接时设备会弹出授权提示，选择"允许"

### 4. 验证连接

运行以下命令验证设备连接：
```cmd
adb devices
```

应该看到类似输出：
```
List of devices attached
XXXXXXXXXX      device
```

## 功能特性

### 主要改进

- ✅ **无需第三方软件**: 不再依赖极限手游助手
- ✅ **原生ADB控制**: 使用Android官方调试工具
- ✅ **更好的兼容性**: 支持所有启用USB调试的Android设备
- ✅ **更稳定的连接**: 避免WebSocket连接问题
- ✅ **更快的响应**: 直接与设备通信

### 支持的操作

- **设备检测**: 自动检测已连接的Android设备
- **屏幕截图**: 使用 `adb shell screencap` 进行截图
- **模拟点击**: 使用 `adb shell input tap` 模拟触摸
- **模拟滑动**: 使用 `adb shell input swipe` 模拟滑动手势
- **分辨率获取**: 使用 `adb shell wm size` 获取屏幕尺寸
- **图像识别**: 使用OpenCV进行模板匹配

## 使用方法

1. 确保设备已连接并启用USB调试
2. 将 `adb.exe` 放置在应用程序目录
3. 运行 `auto.exe`
4. 程序会自动：
   - 启动ADB服务器
   - 检测连接的设备
   - 获取设备分辨率
   - 开始自动化操作

## 故障排除

### 常见问题

1. **"未找到adb.exe文件"**
   - 确保 `adb.exe` 在应用程序目录中
   - 检查文件名是否正确

2. **"未找到已连接的设备"**
   - 检查USB连接
   - 确认已启用USB调试
   - 运行 `adb devices` 验证设备状态

3. **"ADB命令执行失败"**
   - 重启ADB服务: `adb kill-server` 然后 `adb start-server`
   - 重新连接设备
   - 检查设备授权状态

4. **截图失败**
   - 确认设备有足够的存储空间
   - 检查应用是否有写入权限

### 网络ADB连接（可选）

如果需要通过WiFi连接设备：

1. 首先通过USB连接设备
2. 运行: `adb tcpip 5555`
3. 断开USB，运行: `adb connect 设备IP:5555`

## 配置文件

### threshold.ini
可选的置信度配置文件，用于调整图像匹配的精度：
```
0.8
```
值范围: 0.0 - 1.0，默认值: 0.7

## 技术说明

本版本移除了以下依赖：
- WebSocket连接相关代码
- Newtonsoft.Json的部分使用
- 极限手游助手相关功能

保留了以下核心功能：
- OpenCV图像处理
- 模板匹配算法
- 自动化游戏逻辑

## 注意事项

- 确保设备屏幕保持亮屏状态
- 避免在自动化过程中手动操作设备
- 定期清理screenshots目录中的截图文件
- 如遇到问题，可尝试重启ADB服务器
