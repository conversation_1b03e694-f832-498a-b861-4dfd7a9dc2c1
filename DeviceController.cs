using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using OpenCvSharp;
using Point = OpenCvSharp.Point;

namespace auto
{
    public class DeviceController
    {
        private string deviceId;
        public int width = 0;
        public int height = 0;

        private string screenshotPath = "";
        private Dictionary<string, Mat> matchs;
        private float threshold = 0.7f;
        private string adbPath;
        private string scrcpyServerPath;
        private Process scrcpyServerProcess;
        private TcpClient scrcpyClient;
        private NetworkStream scrcpyStream;
        private bool useScrcpyForScreenshot = true;
        public DeviceController()
        {
            matchs = new Dictionary<string, Mat>();

            // 设置adb路径为应用程序目录下的adb.exe
            adbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "adb.exe");

            // 设置scrcpy-server路径
            scrcpyServerPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "scrcpy-server.jar");

            var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "threshold.ini");
            if (File.Exists(path))
            {
                threshold = float.Parse(File.ReadAllText(path));
            }
            Console.WriteLine($"本次运行置信度 {threshold}");

            // 检查adb是否存在
            if (!File.Exists(adbPath))
            {
                throw new Exception($"未找到adb.exe文件，请将adb.exe放置在应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
            }

            // 检查scrcpy-server是否存在
            if (!File.Exists(scrcpyServerPath))
            {
                Console.WriteLine($"警告: 未找到scrcpy-server.jar文件，将使用较慢的adb截图方式");
                Console.WriteLine($"如需高速截图，请将scrcpy-server.jar放置在应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
                useScrcpyForScreenshot = false;
            }
        }

        // 执行adb命令
        private async Task<string> ExecuteAdbCommand(string arguments)
        {
            try
            {
                using (var process = new Process())
                {
                    process.StartInfo.FileName = adbPath;
                    process.StartInfo.Arguments = arguments;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.StandardOutputEncoding = Encoding.UTF8;

                    process.Start();

                    string output = await process.StandardOutput.ReadToEndAsync();
                    string error = await process.StandardError.ReadToEndAsync();

                    await Task.Run(() => process.WaitForExit());

                    if (process.ExitCode != 0 && !string.IsNullOrEmpty(error))
                    {
                        throw new Exception($"ADB命令执行失败: {error}");
                    }

                    return output.Trim();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"执行ADB命令失败: {ex.Message}");
            }
        }

        // 初始化连接（替代原来的Connect方法）
        public async Task Connect()
        {
            try
            {
                // 启动adb服务器
                await ExecuteAdbCommand("start-server");
                Console.WriteLine("ADB服务器已启动");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动ADB服务器失败: {ex.Message}");
                throw;
            }
        }

        // 初始化scrcpy-server（仅推送jar文件）
        private async Task InitializeScrcpyServer()
        {
            try
            {
                if (string.IsNullOrEmpty(deviceId))
                {
                    throw new Exception("设备ID未设置，请先获取设备列表");
                }

                Console.WriteLine("正在初始化scrcpy-server...");

                // 推送scrcpy-server到设备
                await ExecuteAdbCommand($"-s {deviceId} push \"{scrcpyServerPath}\" /data/local/tmp/scrcpy-server.jar");

                Console.WriteLine("scrcpy-server初始化成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化scrcpy-server失败: {ex.Message}");
                Console.WriteLine("将回退到adb截图方式");
                useScrcpyForScreenshot = false;
            }
        }

        // 获取设备列表
        public async Task<string> GetDeviceList()
        {
            try
            {
                string output = await ExecuteAdbCommand("devices");
                string[] lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (line.Contains("\tdevice") && !line.StartsWith("List of devices"))
                    {
                        deviceId = line.Split('\t')[0];
                        Console.WriteLine($"找到设备: {deviceId}");

                        // 设备找到后，如果启用了scrcpy，则初始化scrcpy-server
                        if (useScrcpyForScreenshot)
                        {
                            await InitializeScrcpyServer();
                        }

                        return deviceId;
                    }
                }

                throw new Exception("未找到已连接的设备，请确保设备已连接并启用USB调试");
            }
            catch (Exception ex)
            {
                throw new Exception($"获取设备列表失败: {ex.Message}");
            }
        }

        // 截图
        public async Task<string> TakeScreenshot()
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                string screenshotDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "screenshots");
                if (!Directory.Exists(screenshotDir))
                {
                    Directory.CreateDirectory(screenshotDir);
                }

                screenshotPath = Path.Combine(screenshotDir, $"{deviceId}.png");

                if (useScrcpyForScreenshot)
                {
                    // 使用scrcpy-server进行快速截图
                    return await TakeScrcpyScreenshot();
                }
                else
                {
                    // 回退到adb截图方式
                    return await TakeAdbScreenshot();
                }
            }
            catch (Exception ex)
            {
                // 如果scrcpy截图失败，尝试回退到adb
                if (useScrcpyForScreenshot)
                {
                    Console.WriteLine($"scrcpy截图失败，回退到adb方式: {ex.Message}");
                    useScrcpyForScreenshot = false;
                    return await TakeAdbScreenshot();
                }
                throw new Exception($"截图失败: {ex.Message}");
            }
        }

        // 使用adb进行截图
        private async Task<string> TakeAdbScreenshot()
        {
            // 使用adb截图到设备存储
            string deviceScreenshotPath = "/sdcard/screenshot.png";
            await ExecuteAdbCommand($"-s {deviceId} shell screencap -p {deviceScreenshotPath}");

            // 将截图从设备拉取到本地
            await ExecuteAdbCommand($"-s {deviceId} pull {deviceScreenshotPath} \"{screenshotPath}\"");

            // 删除设备上的临时截图文件
            await ExecuteAdbCommand($"-s {deviceId} shell rm {deviceScreenshotPath}");

            if (File.Exists(screenshotPath))
            {
                return screenshotPath;
            }

            throw new Exception("截图文件未生成");
        }

        // 使用scrcpy-server进行快速截图
        private async Task<string> TakeScrcpyScreenshot()
        {
            try
            {
                // 使用scrcpy-server的截图功能，直接输出到文件
                string deviceScreenshotPath = "/data/local/tmp/scrcpy_screenshot.png";

                // 使用scrcpy-server进行截图
                await ExecuteAdbCommand($"-s {deviceId} shell \"CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture > {deviceScreenshotPath}\"");

                // 将截图从设备拉取到本地
                await ExecuteAdbCommand($"-s {deviceId} pull {deviceScreenshotPath} \"{screenshotPath}\"");

                // 删除设备上的临时截图文件
                await ExecuteAdbCommand($"-s {deviceId} shell rm {deviceScreenshotPath}");

                if (File.Exists(screenshotPath))
                {
                    return screenshotPath;
                }

                throw new Exception("scrcpy截图文件未生成");
            }
            catch (Exception ex)
            {
                throw new Exception($"scrcpy截图失败: {ex.Message}");
            }
        }



        public async Task GetDeviceInfo()
        {
            try
            {
                // 首先尝试通过adb命令获取屏幕分辨率
                string sizeOutput = await ExecuteAdbCommand($"-s {deviceId} shell wm size");

                // 解析输出，格式通常是 "Physical size: 1080x2340"
                if (sizeOutput.Contains("x"))
                {
                    string[] parts = sizeOutput.Split(':');
                    if (parts.Length > 1)
                    {
                        string[] dimensions = parts[1].Trim().Split('x');
                        if (dimensions.Length == 2)
                        {
                            width = int.Parse(dimensions[1]);
                            height = int.Parse(dimensions[0]);
                            Console.WriteLine($"设备分辨率：{width} x {height}");
                            return;
                        }
                    }
                }

                // 如果adb命令获取失败，则通过截图获取分辨率
                Console.WriteLine("通过adb获取分辨率失败，尝试通过截图获取...");
                string filename = await TakeScreenshot();

                using (var bitmap = new Bitmap(filename))
                {
                    width = bitmap.Width;
                    height = bitmap.Height;
                    Console.WriteLine($"设备分辨率：{width} x {height}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取设备信息失败: {ex.Message}");
            }
        }

        // 模拟点击
        public async Task Tap(float x, float y)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                // 如果坐标是相对坐标（0-1之间），转换为绝对坐标
                if (x <= 1)
                {
                    x *= width;
                }
                if (y <= 1)
                {
                    y *= height;
                }

                // 使用adb shell input tap命令模拟点击
                await ExecuteAdbCommand($"-s {deviceId} shell input tap {(int)x} {(int)y}");
            }
            catch (Exception ex)
            {
                throw new Exception($"点击失败: {ex.Message}");
            }
        }

        // 模拟滑动
        public async Task Swipe(float startX, float startY, float endX, float endY)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                // 如果坐标是相对坐标（0-1之间），转换为绝对坐标
                if (startX <= 1)
                {
                    startX *= width;
                }
                if (startY <= 1)
                {
                    startY *= height;
                }
                if (endX <= 1)
                {
                    endX *= width;
                }
                if (endY <= 1)
                {
                    endY *= height;
                }

                // 使用adb shell input swipe命令模拟滑动，持续时间500ms
                await ExecuteAdbCommand($"-s {deviceId} shell input swipe {(int)startX} {(int)startY} {(int)endX} {(int)endY} 500");
            }
            catch (Exception ex)
            {
                throw new Exception($"滑动失败: {ex.Message}");
            }
        }

        public async Task<string> MatchImagesAndTap(string[] list)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }
            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }

            Mat source1 = Cv2.ImRead(screenshotPath);

            // 检查图像是否成功加载
            if (source1.Empty())
            {
                throw new Exception("无法加载截图文件");
            }

            // 将截图缩放到标准尺寸 1080x488
            const int targetWidth = 1080;
            const int targetHeight = 488;
            Mat source = new Mat();
            Cv2.Resize(source1, source, new OpenCvSharp.Size(targetWidth, targetHeight));
            source1.Release();
            Point MaxPoint = new Point();
            float maxT = 0;
            string key = "";
            foreach (var item in list)
            {
                if (!matchs.TryGetValue(item, out Mat template))
                {
                    string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", $"{item}.png");
                    if (!File.Exists(templatePath))
                    {
                        throw new Exception("图片不存在");
                    }
                    template = Cv2.ImRead(templatePath);
                    matchs[item] = template;
                }
                if (template.Empty())
                {
                    continue;
                }
                Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1);

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点（在1080x488缩放图像中的坐标）
                Point centerPoint = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );
                result.Release();
                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    continue;
                }
                if (confidence > maxT)
                {
                    maxT = (float)confidence;
                    MaxPoint = centerPoint;
                    key = item;
                    if (key == "fa")
                    {
                        break;
                    }
                }
            }
            source.Release();
            if (maxT > 0)
            {
                // 将1080x488坐标转换为实际设备坐标
                float actualX = (float)MaxPoint.X * width / targetWidth;
                float actualY = (float)MaxPoint.Y * height / targetHeight;
                await Tap(actualX, actualY);
            }

            return key;
        }

        // 使用OpenCV模板匹配在屏幕中查找小图
        public async Task<Point> FindImageOnScreen(string imgName)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", imgName);
            if (!File.Exists(templatePath))
            {
                throw new Exception("图片不存在");
            }
            Mat sourceOriginal = Cv2.ImRead(screenshotPath);
            if (sourceOriginal.Empty())
            {
                throw new Exception("无法加载截图文件");
            }

            // 将截图缩放到标准尺寸 1080x488
            const int targetWidth = 1080;
            const int targetHeight = 488;
            Mat source = new Mat();
            Cv2.Resize(sourceOriginal, source, new OpenCvSharp.Size(targetWidth, targetHeight));
            sourceOriginal.Release();

            using (Mat template = Cv2.ImRead(templatePath))
            using (Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1))
            {
                // 检查模板图片是否成功加载
                if (template.Empty())
                {
                    throw new Exception($"无法加载模板图片: {templatePath}");
                }

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点（在1080x488缩放图像中的坐标）
                Point centerPointScaled = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );

                result.Release();
                source.Release();
                template.Release();

                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    return new Point(-1, -1);
                }

                // 将1080x488坐标转换为实际设备坐标
                Point actualCenterPoint = new Point(
                    (int)(centerPointScaled.X * width / (float)targetWidth),
                    (int)(centerPointScaled.Y * height / (float)targetHeight)
                );

                return actualCenterPoint;
            }
        }

        // 关闭连接
        public void Close()
        {
            try
            {
                // 清理scrcpy相关资源
                if (scrcpyStream != null)
                {
                    scrcpyStream.Close();
                    scrcpyStream = null;
                }

                if (scrcpyClient != null)
                {
                    scrcpyClient.Close();
                    scrcpyClient = null;
                }

                if (scrcpyServerProcess != null && !scrcpyServerProcess.HasExited)
                {
                    scrcpyServerProcess.Kill();
                    scrcpyServerProcess = null;
                }

                // 清理端口转发
                if (!string.IsNullOrEmpty(deviceId))
                {
                    try
                    {
                        ExecuteAdbCommand($"-s {deviceId} forward --remove tcp:27183").Wait();
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }

                // 释放图像匹配缓存
                if (matchs != null)
                {
                    foreach (var mat in matchs.Values)
                    {
                        mat?.Release();
                    }
                    matchs.Clear();
                }

                Console.WriteLine("资源已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理资源时出错: {ex.Message}");
            }
        }
    }

    public class ColorInfo
    {
        public byte R { get; set; }
        public byte G { get; set; }
        public byte B { get; set; }
        public byte A { get; set; }
        public string Hex { get; set; }
    }
}