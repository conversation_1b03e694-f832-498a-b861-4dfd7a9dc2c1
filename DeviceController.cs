using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using OpenCvSharp;
using Point = OpenCvSharp.Point;

namespace auto
{
    public class DeviceController
    {
        private string deviceId;
        public int width = 0;
        public int height = 0;

        private string screenshotPath = "";
        private Dictionary<string, Mat> matchs;
        private float threshold = 0.7f;
        private string adbPath;
        private string scrcpyServerPath;
        private Process scrcpyServerProcess;
        private TcpClient scrcpyClient;
        private NetworkStream scrcpyStream;
        private TcpClient scrcpyControlClient;
        private NetworkStream scrcpyControlStream;
        private bool useScrcpyForScreenshot = true;
        private bool useScrcpyForControl = true;
        public DeviceController()
        {
            matchs = new Dictionary<string, Mat>();

            // 设置adb路径为应用程序目录下的adb.exe
            adbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "adb.exe");

            // 设置scrcpy-server路径
            scrcpyServerPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "scrcpy-server.jar");

            var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "threshold.ini");
            if (File.Exists(path))
            {
                threshold = float.Parse(File.ReadAllText(path));
            }
            Console.WriteLine($"本次运行置信度 {threshold}");

            // 检查adb是否存在
            if (!File.Exists(adbPath))
            {
                throw new Exception($"未找到adb.exe文件，请将adb.exe放置在应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
            }

            // 检查scrcpy-server是否存在
            if (!File.Exists(scrcpyServerPath))
            {
                Console.WriteLine($"警告: 未找到scrcpy-server.jar文件，将使用adb方式");
                Console.WriteLine($"如需高速截图和控制，请将scrcpy-server.jar放置在应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
                useScrcpyForScreenshot = false;
                useScrcpyForControl = false;
            }
        }

        // 执行adb命令
        private async Task<string> ExecuteAdbCommand(string arguments)
        {
            try
            {
                using (var process = new Process())
                {
                    process.StartInfo.FileName = adbPath;
                    process.StartInfo.Arguments = arguments;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.StandardOutputEncoding = Encoding.UTF8;

                    process.Start();

                    string output = await process.StandardOutput.ReadToEndAsync();
                    string error = await process.StandardError.ReadToEndAsync();

                    await Task.Run(() => process.WaitForExit());

                    if (process.ExitCode != 0 && !string.IsNullOrEmpty(error))
                    {
                        throw new Exception($"ADB命令执行失败: {error}");
                    }

                    return output.Trim();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"执行ADB命令失败: {ex.Message}");
            }
        }

        // 初始化连接（替代原来的Connect方法）
        public async Task Connect()
        {
            try
            {
                // 启动adb服务器
                await ExecuteAdbCommand("start-server");
                Console.WriteLine("ADB服务器已启动");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动ADB服务器失败: {ex.Message}");
                throw;
            }
        }

        // 初始化scrcpy-server（仅推送jar文件，不启动服务）
        private async Task InitializeScrcpyServer()
        {
            try
            {
                if (string.IsNullOrEmpty(deviceId))
                {
                    throw new Exception("设备ID未设置，请先获取设备列表");
                }

                Console.WriteLine("正在初始化scrcpy-server...");

                // 推送scrcpy-server到设备
                await ExecuteAdbCommand($"-s {deviceId} push \"{scrcpyServerPath}\" /data/local/tmp/scrcpy-server.jar");

                // 验证文件是否推送成功
                string checkResult = await ExecuteAdbCommand($"-s {deviceId} shell ls -la /data/local/tmp/scrcpy-server.jar");
                if (checkResult.Contains("No such file"))
                {
                    throw new Exception("scrcpy-server.jar推送失败");
                }

                Console.WriteLine("scrcpy-server初始化成功（仅推送jar文件）");
                Console.WriteLine("将使用scrcpy的ScreenCapture类进行截图");

                // 测试scrcpy是否可用
                await TestScrcpyAvailability();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"初始化scrcpy-server失败: {ex.Message}");
                Console.WriteLine("将回退到adb方式");
                useScrcpyForScreenshot = false;
                useScrcpyForControl = false;
            }
        }

        // 测试scrcpy是否可用
        private async Task TestScrcpyAvailability()
        {
            try
            {
                Console.WriteLine("测试scrcpy可用性...");

                // 尝试运行一个简单的scrcpy命令来测试
                string testCommand = $"-s {deviceId} shell \"CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture --help 2>&1 || echo 'test_complete'\"";
                string testOutput = await ExecuteAdbCommand(testCommand);

                Console.WriteLine($"scrcpy测试输出: {testOutput?.Substring(0, Math.Min(100, testOutput?.Length ?? 0))}");

                // 如果包含错误信息，说明scrcpy不可用
                if (testOutput.Contains("ClassNotFoundException") ||
                    testOutput.Contains("NoClassDefFoundError") ||
                    testOutput.Contains("FATAL") ||
                    testOutput.Contains("Aborted"))
                {
                    throw new Exception($"scrcpy不兼容此设备: {testOutput}");
                }

                Console.WriteLine("scrcpy可用性测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"scrcpy可用性测试失败: {ex.Message}");
                throw;
            }
        }

        // 获取设备列表
        public async Task<string> GetDeviceList()
        {
            try
            {
                string output = await ExecuteAdbCommand("devices");
                string[] lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (line.Contains("\tdevice") && !line.StartsWith("List of devices"))
                    {
                        deviceId = line.Split('\t')[0];
                        Console.WriteLine($"找到设备: {deviceId}");

                        // 设备找到后，如果启用了scrcpy，则初始化scrcpy-server
                        if (useScrcpyForScreenshot)
                        {
                            await InitializeScrcpyServer();
                        }

                        return deviceId;
                    }
                }

                throw new Exception("未找到已连接的设备，请确保设备已连接并启用USB调试");
            }
            catch (Exception ex)
            {
                throw new Exception($"获取设备列表失败: {ex.Message}");
            }
        }

        // 截图
        public async Task<string> TakeScreenshot()
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                string screenshotDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "screenshots");
                if (!Directory.Exists(screenshotDir))
                {
                    Directory.CreateDirectory(screenshotDir);
                }

                screenshotPath = Path.Combine(screenshotDir, $"{deviceId}.png");

                if (useScrcpyForScreenshot)
                {
                    // 使用scrcpy-server进行快速截图
                    return await TakeScrcpyScreenshot();
                }
                else
                {
                    // 回退到adb截图方式
                    return await TakeAdbScreenshot();
                }
            }
            catch (Exception ex)
            {
                // 如果scrcpy截图失败，尝试回退到adb
                if (useScrcpyForScreenshot)
                {
                    Console.WriteLine($"scrcpy截图失败，回退到adb方式: {ex.Message}");
                    useScrcpyForScreenshot = false;
                    return await TakeAdbScreenshot();
                }
                throw new Exception($"截图失败: {ex.Message}");
            }
        }

        // 使用adb进行截图
        private async Task<string> TakeAdbScreenshot()
        {
            // 使用adb截图到设备存储
            string deviceScreenshotPath = "/sdcard/screenshot.png";
            await ExecuteAdbCommand($"-s {deviceId} shell screencap -p {deviceScreenshotPath}");

            // 将截图从设备拉取到本地
            await ExecuteAdbCommand($"-s {deviceId} pull {deviceScreenshotPath} \"{screenshotPath}\"");

            // 删除设备上的临时截图文件
            await ExecuteAdbCommand($"-s {deviceId} shell rm {deviceScreenshotPath}");

            if (File.Exists(screenshotPath))
            {
                return screenshotPath;
            }

            throw new Exception("截图文件未生成");
        }

        // 使用scrcpy-server进行快速截图
        private async Task<string> TakeScrcpyScreenshot()
        {
            try
            {
                Console.WriteLine("使用scrcpy进行截图...");

                // 方法1: 尝试使用scrcpy的ScreenCapture类（输出到文件）
                string deviceScreenshotPath = "/data/local/tmp/scrcpy_screenshot.png";

                try
                {
                    // 先清理可能存在的旧文件
                    await ExecuteAdbCommand($"-s {deviceId} shell rm -f {deviceScreenshotPath}");

                    // 使用scrcpy-server进行截图并保存到设备文件
                    string captureCommand = $"-s {deviceId} shell \"CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture > {deviceScreenshotPath}\"";
                    await ExecuteAdbCommand(captureCommand);

                    // 检查文件是否生成
                    string checkCommand = $"-s {deviceId} shell \"ls -la {deviceScreenshotPath}\"";
                    string fileInfo = await ExecuteAdbCommand(checkCommand);

                    if (fileInfo.Contains("No such file") || fileInfo.Contains("not found"))
                    {
                        throw new Exception("截图文件未在设备上生成");
                    }

                    // 将截图从设备拉取到本地
                    await ExecuteAdbCommand($"-s {deviceId} pull {deviceScreenshotPath} \"{screenshotPath}\"");

                    // 删除设备上的临时文件
                    await ExecuteAdbCommand($"-s {deviceId} shell rm -f {deviceScreenshotPath}");

                    // 验证本地文件
                    if (File.Exists(screenshotPath) && new FileInfo(screenshotPath).Length > 1000)
                    {
                        Console.WriteLine($"scrcpy截图成功，文件大小: {new FileInfo(screenshotPath).Length} 字节");
                        return screenshotPath;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"scrcpy方法1失败: {ex.Message}");
                }

                // 方法2: 尝试直接获取base64输出
                try
                {
                    Console.WriteLine("尝试scrcpy方法2（base64输出）...");
                    string captureCommand = $"-s {deviceId} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture";
                    string output = await ExecuteAdbCommand(captureCommand);

                    if (!string.IsNullOrEmpty(output) && output.Length > 100 &&
                        !output.Contains("Error") && !output.Contains("Exception") &&
                        !output.Contains("Aborted") && !output.Contains("FATAL"))
                    {
                        // 清理输出
                        string cleanOutput = output.Trim().Replace("\r", "").Replace("\n", "");

                        // 验证是否是有效的base64
                        if (IsValidBase64(cleanOutput))
                        {
                            byte[] imageBytes = Convert.FromBase64String(cleanOutput);
                            File.WriteAllBytes(screenshotPath, imageBytes);

                            if (File.Exists(screenshotPath) && new FileInfo(screenshotPath).Length > 1000)
                            {
                                Console.WriteLine($"scrcpy截图成功（base64），文件大小: {new FileInfo(screenshotPath).Length} 字节");
                                return screenshotPath;
                            }
                        }
                    }

                    Console.WriteLine($"scrcpy输出无效，长度: {output?.Length ?? 0}，内容预览: {output?.Substring(0, Math.Min(50, output?.Length ?? 0))}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"scrcpy方法2失败: {ex.Message}");
                }

                // 方法3: 尝试使用更简单的调用方式
                try
                {
                    Console.WriteLine("尝试scrcpy方法3（简化调用）...");

                    // 使用更简单的命令格式
                    string simpleCommand = $"-s {deviceId} shell \"cd /data/local/tmp && CLASSPATH=scrcpy-server.jar app_process . com.genymobile.scrcpy.wrappers.ScreenCapture\"";
                    string output = await ExecuteAdbCommand(simpleCommand);

                    if (!string.IsNullOrEmpty(output) && output.Length > 100 &&
                        !output.Contains("Error") && !output.Contains("Exception") &&
                        !output.Contains("Aborted") && !output.Contains("FATAL"))
                    {
                        string cleanOutput = output.Trim().Replace("\r", "").Replace("\n", "");

                        if (IsValidBase64(cleanOutput))
                        {
                            byte[] imageBytes = Convert.FromBase64String(cleanOutput);
                            File.WriteAllBytes(screenshotPath, imageBytes);

                            if (File.Exists(screenshotPath) && new FileInfo(screenshotPath).Length > 1000)
                            {
                                Console.WriteLine($"scrcpy截图成功（简化调用），文件大小: {new FileInfo(screenshotPath).Length} 字节");
                                return screenshotPath;
                            }
                        }
                    }

                    Console.WriteLine($"scrcpy方法3输出: {output?.Substring(0, Math.Min(100, output?.Length ?? 0))}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"scrcpy方法3失败: {ex.Message}");
                }

                throw new Exception("所有scrcpy截图方法都失败了");
            }
            catch (Exception ex)
            {
                throw new Exception($"scrcpy截图失败: {ex.Message}");
            }
        }

        // 验证字符串是否是有效的base64
        private bool IsValidBase64(string base64String)
        {
            if (string.IsNullOrEmpty(base64String) || base64String.Length < 100)
                return false;

            try
            {
                // base64字符串长度必须是4的倍数
                if (base64String.Length % 4 != 0)
                    return false;

                // 尝试解码一小部分来验证
                Convert.FromBase64String(base64String.Substring(0, Math.Min(100, base64String.Length)));
                return true;
            }
            catch
            {
                return false;
            }
        }



        public async Task GetDeviceInfo()
        {
            try
            {
                // 首先尝试通过adb命令获取屏幕分辨率
                string sizeOutput = await ExecuteAdbCommand($"-s {deviceId} shell wm size");

                // 解析输出，格式通常是 "Physical size: 1080x2340"
                if (sizeOutput.Contains("x"))
                {
                    string[] parts = sizeOutput.Split(':');
                    if (parts.Length > 1)
                    {
                        string[] dimensions = parts[1].Trim().Split('x');
                        if (dimensions.Length == 2)
                        {
                            width = int.Parse(dimensions[0]);
                            height = int.Parse(dimensions[1]);
                            Console.WriteLine($"设备分辨率：{width} x {height}");
                            return;
                        }
                    }
                }

                // 如果adb命令获取失败，则通过截图获取分辨率
                Console.WriteLine("通过adb获取分辨率失败，尝试通过截图获取...");
                string filename = await TakeScreenshot();

                using (var bitmap = new Bitmap(filename))
                {
                    width = bitmap.Width;
                    height = bitmap.Height;
                    Console.WriteLine($"设备分辨率：{width} x {height}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取设备信息失败: {ex.Message}");
            }
        }

        // 模拟点击
        public async Task Tap(float x, float y)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                // 如果坐标是相对坐标（0-1之间），转换为绝对坐标
                if (x <= 1)
                {
                    x *= width;
                }
                if (y <= 1)
                {
                    y *= height;
                }

                // 暂时禁用scrcpy控制，使用adb控制
                await ExecuteAdbCommand($"-s {deviceId} shell input tap {(int)x} {(int)y}");
            }
            catch (Exception ex)
            {
                throw new Exception($"点击失败: {ex.Message}");
            }
        }

        // 模拟滑动
        public async Task Swipe(float startX, float startY, float endX, float endY)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                // 如果坐标是相对坐标（0-1之间），转换为绝对坐标
                if (startX <= 1)
                {
                    startX *= width;
                }
                if (startY <= 1)
                {
                    startY *= height;
                }
                if (endX <= 1)
                {
                    endX *= width;
                }
                if (endY <= 1)
                {
                    endY *= height;
                }

                // 暂时禁用scrcpy控制，使用adb控制
                await ExecuteAdbCommand($"-s {deviceId} shell input swipe {(int)startX} {(int)startY} {(int)endX} {(int)endY} 500");
            }
            catch (Exception ex)
            {
                throw new Exception($"滑动失败: {ex.Message}");
            }
        }

        public async Task<string> MatchImagesAndTap(string[] list)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }
            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }

            Mat source1 = Cv2.ImRead(screenshotPath);

            // 检查图像是否成功加载
            if (source1.Empty())
            {
                throw new Exception("无法加载截图文件");
            }

            // 将截图缩放到标准尺寸 1080x488
            const int targetWidth = 1080;
            const int targetHeight = 488;
            Mat source = new Mat();
            Cv2.Resize(source1, source, new OpenCvSharp.Size(targetWidth, targetHeight));
            source1.Release();
            Point MaxPoint = new Point();
            float maxT = 0;
            string key = "";
            foreach (var item in list)
            {
                if (!matchs.TryGetValue(item, out Mat template))
                {
                    string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", $"{item}.png");
                    if (!File.Exists(templatePath))
                    {
                        throw new Exception("图片不存在");
                    }
                    template = Cv2.ImRead(templatePath);
                    matchs[item] = template;
                }
                if (template.Empty())
                {
                    continue;
                }
                Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1);

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点（在1080x488缩放图像中的坐标）
                Point centerPoint = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );
                result.Release();
                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    continue;
                }
                if (confidence > maxT)
                {
                    maxT = (float)confidence;
                    MaxPoint = centerPoint;
                    key = item;
                    if (key == "fa")
                    {
                        break;
                    }
                }
            }
            source.Release();
            if (maxT > 0)
            {
                // 将1080x488坐标转换为实际设备坐标
                float actualX = (float)MaxPoint.X * width / targetWidth;
                float actualY = (float)MaxPoint.Y * height / targetHeight;
                await Tap(actualX, actualY);
            }

            return key;
        }

        // 使用OpenCV模板匹配在屏幕中查找小图
        public async Task<Point> FindImageOnScreen(string imgName)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", imgName);
            if (!File.Exists(templatePath))
            {
                throw new Exception("图片不存在");
            }
            Mat sourceOriginal = Cv2.ImRead(screenshotPath);
            if (sourceOriginal.Empty())
            {
                throw new Exception("无法加载截图文件");
            }

            // 将截图缩放到标准尺寸 1080x488
            const int targetWidth = 1080;
            const int targetHeight = 488;
            Mat source = new Mat();
            Cv2.Resize(sourceOriginal, source, new OpenCvSharp.Size(targetWidth, targetHeight));
            sourceOriginal.Release();

            using (Mat template = Cv2.ImRead(templatePath))
            using (Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1))
            {
                // 检查模板图片是否成功加载
                if (template.Empty())
                {
                    throw new Exception($"无法加载模板图片: {templatePath}");
                }

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点（在1080x488缩放图像中的坐标）
                Point centerPointScaled = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );

                result.Release();
                source.Release();
                template.Release();

                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    return new Point(-1, -1);
                }

                // 将1080x488坐标转换为实际设备坐标
                Point actualCenterPoint = new Point(
                    (int)(centerPointScaled.X * width / (float)targetWidth),
                    (int)(centerPointScaled.Y * height / (float)targetHeight)
                );

                return actualCenterPoint;
            }
        }

        // scrcpy点击控制
        private async Task ScrcpyTap(int x, int y)
        {
            try
            {
                // scrcpy触摸事件协议
                // 按下事件
                await SendScrcpyTouchEvent(0, x, y, 1.0f, 0); // ACTION_DOWN
                await Task.Delay(10); // 短暂延迟
                // 抬起事件
                await SendScrcpyTouchEvent(1, x, y, 1.0f, 0); // ACTION_UP
            }
            catch (Exception ex)
            {
                throw new Exception($"scrcpy点击失败: {ex.Message}");
            }
        }

        // scrcpy滑动控制
        private async Task ScrcpySwipe(int startX, int startY, int endX, int endY)
        {
            try
            {
                // 计算滑动步数
                int steps = 20;
                float deltaX = (float)(endX - startX) / steps;
                float deltaY = (float)(endY - startY) / steps;

                // 按下事件
                await SendScrcpyTouchEvent(0, startX, startY, 1.0f, 0); // ACTION_DOWN
                await Task.Delay(10);

                // 移动事件
                for (int i = 1; i <= steps; i++)
                {
                    int currentX = startX + (int)(deltaX * i);
                    int currentY = startY + (int)(deltaY * i);
                    await SendScrcpyTouchEvent(2, currentX, currentY, 1.0f, 0); // ACTION_MOVE
                    await Task.Delay(10);
                }

                // 抬起事件
                await SendScrcpyTouchEvent(1, endX, endY, 1.0f, 0); // ACTION_UP
            }
            catch (Exception ex)
            {
                throw new Exception($"scrcpy滑动失败: {ex.Message}");
            }
        }

        // 发送scrcpy触摸事件
        private async Task SendScrcpyTouchEvent(int action, int x, int y, float pressure, long pointerId)
        {
            try
            {
                // scrcpy控制消息格式
                byte[] message = new byte[28];

                // 消息类型 (1 byte) - INJECT_TOUCH_EVENT = 2
                message[0] = 2;

                // 动作 (1 byte)
                message[1] = (byte)action;

                // 指针ID (8 bytes, big-endian)
                byte[] pointerIdBytes = BitConverter.GetBytes(pointerId);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(pointerIdBytes);
                Array.Copy(pointerIdBytes, 0, message, 2, 8);

                // X坐标 (4 bytes, big-endian)
                byte[] xBytes = BitConverter.GetBytes(x);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(xBytes);
                Array.Copy(xBytes, 0, message, 10, 4);

                // Y坐标 (4 bytes, big-endian)
                byte[] yBytes = BitConverter.GetBytes(y);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(yBytes);
                Array.Copy(yBytes, 0, message, 14, 4);

                // 屏幕宽度 (2 bytes, big-endian)
                byte[] widthBytes = BitConverter.GetBytes((ushort)width);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(widthBytes);
                Array.Copy(widthBytes, 0, message, 18, 2);

                // 屏幕高度 (2 bytes, big-endian)
                byte[] heightBytes = BitConverter.GetBytes((ushort)height);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(heightBytes);
                Array.Copy(heightBytes, 0, message, 20, 2);

                // 压力值 (2 bytes, big-endian) - 转换为0-65535范围
                ushort pressureValue = (ushort)(pressure * 65535);
                byte[] pressureBytes = BitConverter.GetBytes(pressureValue);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(pressureBytes);
                Array.Copy(pressureBytes, 0, message, 22, 2);

                // 按钮状态 (4 bytes, big-endian)
                byte[] buttonBytes = BitConverter.GetBytes(0);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(buttonBytes);
                Array.Copy(buttonBytes, 0, message, 24, 4);

                // 发送消息
                await scrcpyControlStream.WriteAsync(message, 0, message.Length);
                await scrcpyControlStream.FlushAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"发送scrcpy触摸事件失败: {ex.Message}");
            }
        }

        // scrcpy息屏功能
        public async Task TurnOffScreen()
        {
            try
            {
                if (useScrcpyForControl && scrcpyControlStream != null)
                {
                    // 使用scrcpy控制协议发送息屏命令
                    await SendScrcpyPowerEvent(0); // POWER_MODE_OFF
                    Console.WriteLine("已通过scrcpy发送息屏命令");
                }
                else
                {
                    // 回退到adb命令
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_POWER");
                    Console.WriteLine("已通过adb发送息屏命令");
                }
            }
            catch (Exception ex)
            {
                // 如果scrcpy控制失败，尝试回退到adb
                if (useScrcpyForControl)
                {
                    Console.WriteLine($"scrcpy息屏失败，回退到adb方式: {ex.Message}");
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_POWER");
                }
                else
                {
                    throw new Exception($"息屏失败: {ex.Message}");
                }
            }
        }

        // scrcpy亮屏功能
        public async Task TurnOnScreen()
        {
            try
            {
                if (useScrcpyForControl && scrcpyControlStream != null)
                {
                    // 使用scrcpy控制协议发送亮屏命令
                    await SendScrcpyPowerEvent(2); // POWER_MODE_NORMAL
                    Console.WriteLine("已通过scrcpy发送亮屏命令");
                }
                else
                {
                    // 回退到adb命令
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_WAKEUP");
                    Console.WriteLine("已通过adb发送亮屏命令");
                }
            }
            catch (Exception ex)
            {
                // 如果scrcpy控制失败，尝试回退到adb
                if (useScrcpyForControl)
                {
                    Console.WriteLine($"scrcpy亮屏失败，回退到adb方式: {ex.Message}");
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_WAKEUP");
                }
                else
                {
                    throw new Exception($"亮屏失败: {ex.Message}");
                }
            }
        }

        // 发送scrcpy电源事件
        private async Task SendScrcpyPowerEvent(int powerMode)
        {
            try
            {
                // scrcpy电源控制消息格式
                byte[] message = new byte[2];

                // 消息类型 (1 byte) - SET_SCREEN_POWER_MODE = 10
                message[0] = 10;

                // 电源模式 (1 byte)
                // 0 = POWER_MODE_OFF (息屏)
                // 2 = POWER_MODE_NORMAL (正常/亮屏)
                message[1] = (byte)powerMode;

                // 发送消息
                await scrcpyControlStream.WriteAsync(message, 0, message.Length);
                await scrcpyControlStream.FlushAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"发送scrcpy电源事件失败: {ex.Message}");
            }
        }

        // 切换屏幕状态（息屏/亮屏）
        public async Task ToggleScreen()
        {
            try
            {
                bool isScreenOn = await IsScreenOn();
                if (isScreenOn)
                {
                    await TurnOffScreen();
                    Console.WriteLine("屏幕已息屏");
                }
                else
                {
                    await TurnOnScreen();
                    Console.WriteLine("屏幕已亮屏");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"切换屏幕状态失败: {ex.Message}");
            }
        }

        // 检查屏幕是否亮屏
        public async Task<bool> IsScreenOn()
        {
            try
            {
                // 使用adb命令检查屏幕状态
                string output = await ExecuteAdbCommand($"-s {deviceId} shell dumpsys power | grep \"mHoldingDisplaySuspendBlocker\"");

                // 如果包含"mHoldingDisplaySuspendBlocker=true"，说明屏幕是亮的
                bool isOn = output.Contains("mHoldingDisplaySuspendBlocker=true");

                // 如果上面的方法不工作，尝试另一种方法
                if (string.IsNullOrEmpty(output))
                {
                    output = await ExecuteAdbCommand($"-s {deviceId} shell dumpsys power | grep \"Display Power\"");
                    isOn = output.Contains("state=ON") || output.Contains("state=2");
                }

                return isOn;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查屏幕状态失败: {ex.Message}");
                // 如果检查失败，默认假设屏幕是亮的
                return true;
            }
        }

        // 息屏并等待指定时间后自动亮屏
        public async Task TurnOffScreenTemporarily(int seconds)
        {
            try
            {
                Console.WriteLine($"息屏 {seconds} 秒...");
                await TurnOffScreen();

                // 等待指定时间
                await Task.Delay(seconds * 1000);

                await TurnOnScreen();
                Console.WriteLine("自动亮屏完成");
            }
            catch (Exception ex)
            {
                throw new Exception($"临时息屏失败: {ex.Message}");
            }
        }

        // 关闭连接
        public void Close()
        {
            try
            {
                // 清理scrcpy相关资源
                if (scrcpyStream != null)
                {
                    scrcpyStream.Close();
                    scrcpyStream = null;
                }

                if (scrcpyClient != null)
                {
                    scrcpyClient.Close();
                    scrcpyClient = null;
                }

                if (scrcpyControlStream != null)
                {
                    scrcpyControlStream.Close();
                    scrcpyControlStream = null;
                }

                if (scrcpyControlClient != null)
                {
                    scrcpyControlClient.Close();
                    scrcpyControlClient = null;
                }

                if (scrcpyServerProcess != null && !scrcpyServerProcess.HasExited)
                {
                    scrcpyServerProcess.Kill();
                    scrcpyServerProcess = null;
                }

                // 清理端口转发
                if (!string.IsNullOrEmpty(deviceId))
                {
                    try
                    {
                        ExecuteAdbCommand($"-s {deviceId} forward --remove tcp:27183").Wait();
                        ExecuteAdbCommand($"-s {deviceId} forward --remove tcp:27184").Wait();
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }

                // 释放图像匹配缓存
                if (matchs != null)
                {
                    foreach (var mat in matchs.Values)
                    {
                        mat?.Release();
                    }
                    matchs.Clear();
                }

                Console.WriteLine("资源已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理资源时出错: {ex.Message}");
            }
        }
    }

    public class ColorInfo
    {
        public byte R { get; set; }
        public byte G { get; set; }
        public byte B { get; set; }
        public byte A { get; set; }
        public string Hex { get; set; }
    }
}