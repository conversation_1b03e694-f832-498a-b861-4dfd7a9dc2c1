using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OpenCvSharp;
using static System.Net.Mime.MediaTypeNames;
using Point = OpenCvSharp.Point;

namespace auto
{
    public class DeviceController
    {
        private ClientWebSocket ws;
        private string deviceId;
        public int width = 0;
        public int height = 0;

        // WebSocket服务器地址
        private const string WS_URL = "ws://127.0.0.1:33331";

        private string screenshotPath = "";

        private Dictionary<string, Mat> matchs;

        private float threshold = 0.7f;
        public DeviceController()
        {
            matchs = new Dictionary<string, Mat>();
            var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "threshold.ini");
            if (File.Exists(path))
            {
                threshold = float.Parse(File.ReadAllText(path));
            }
            Console.WriteLine($"本次运行置信度 {threshold}");
        }

        // 连接到WebSocket服务器
        public async Task Connect()
        {
            ws = new ClientWebSocket();
            try
            {
                await ws.ConnectAsync(new Uri(WS_URL), CancellationToken.None);
                Console.WriteLine("已连接到极限手游助手");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"WebSocket连接错误: {ex.Message}");
                throw;
            }
        }

        // 发送消息到服务器
        public async Task<string> SendMessage(object message)
        {
            if (ws == null || ws.State != WebSocketState.Open)
            {
                throw new Exception("WebSocket未连接");
            }

            string messageStr = JsonConvert.SerializeObject(message);
            //Console.WriteLine(messageStr);
            byte[] messageBytes = Encoding.UTF8.GetBytes(messageStr);
            await ws.SendAsync(new ArraySegment<byte>(messageBytes), WebSocketMessageType.Text, true, CancellationToken.None);

            // 接收响应
            byte[] buffer = new byte[4096];
            WebSocketReceiveResult result = await ws.ReceiveAsync(new ArraySegment<byte>(buffer), CancellationToken.None);
            string response = Encoding.UTF8.GetString(buffer, 0, result.Count);
            var responseObj = JObject.Parse(response);
            if (responseObj != null && responseObj["statusCode"]?.Value<int>() == 200)
            {
                return responseObj["result"]?.ToString();
            }
            else
            {
                throw new Exception("操作失败");
            }
        }

        // 获取设备列表
        public async Task<string> GetDeviceList()
        {
            string response = await SendMessage(new { action = "list" });
            var devices = JArray.Parse(response);

            if (devices != null && devices.Count > 0)
            {
                deviceId = devices[0]["deviceId"].ToString(); // 默认使用第一个设备
                return deviceId;
            }
            throw new Exception("未找到设备");
        }

        // 截图
        public async Task<string> TakeScreenshot()
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            string screenshotDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "screenshots");
            if (!Directory.Exists(screenshotDir))
            {
                Directory.CreateDirectory(screenshotDir);
            }

            string response = await SendMessage(new
            {
                action = "screen",
                comm = new
                {
                    deviceIds = deviceId,
                    savePath = screenshotDir,
                    onlyDeviceName = 1
                }
            });

            if (response == "OK")
            {
                screenshotPath = Path.Combine(screenshotDir, $"{deviceId}.png");
                return screenshotPath;
            }
            return null;
        }



        public async Task GetDeviceInfo()
        {
            string filename = await TakeScreenshot();

            // 使用System.Drawing获取设备分辨率
            using (var bitmap = new Bitmap(filename))
            {
                width = bitmap.Width;
                height = bitmap.Height;

                Console.WriteLine($"设备分辨率：{width} x {height}");
            }
        }

        private async Task PointerEvent(string mask, float x, float y)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }
            if (x > 1)
            {
                x /= width;
            }
            if (y > 1)
            {
                y /= height;
            }

            await SendMessage(new
            {
                action = "PointerEvent",
                comm = new
                {
                    deviceIds = deviceId,
                    finger = 1,
                    mask = mask,
                    x = x.ToString(),
                    y = y.ToString(),
                    delta = "0"
                }
            });
        }

        // 模拟点击
        public async Task Tap(float x, float y)
        {
            await PointerEvent("0", x, y);
            await Task.Delay(20);
            await PointerEvent("2", x, y);
        }

        // 模拟滑动
        public async Task Swipe(float startX, float startY, float endX, float endY)
        {
            await PointerEvent("0", startX, startY);

            for (int index = 0; index < 5; index++)
            {
                await PointerEvent(
                    "1",
                    startX + ((endX - startX) / 5) * index,
                    startY + ((endY - startY) / 5) * index
                );
                await Task.Delay(20);
            }

            await PointerEvent("2", endX, endY);
        }

        public async Task<string> MatchImagesAndTap(string[] list)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }
            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }

            Mat source1 = Cv2.ImRead(screenshotPath);

            Rect roi = new Rect(0, 100, width, height - 100);
            Mat source = new Mat(source1, roi);
            source1.Release();
            Point MaxPoint = new Point();
            float maxT = 0;
            string key = "";
            foreach (var item in list)
            {
                if (!matchs.TryGetValue(item, out Mat template))
                {
                    string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", $"{item}.png");
                    if (!File.Exists(templatePath))
                    {
                        throw new Exception("图片不存在");
                    }
                    template = Cv2.ImRead(templatePath);
                    matchs[item] = template;
                }
                if (template.Empty())
                {
                    continue;
                }
                Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1);

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点
                Point centerPoint = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2 + 100
                );
                result.Release();
                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    continue;
                }
                if (confidence > maxT)
                {
                    maxT = (float)confidence;
                    MaxPoint = centerPoint;
                    key = item;
                    if (key == "fa")
                    {
                        break;
                    }
                }
            }
            source.Release();
            if (maxT > 0)
            {
                await Tap(MaxPoint.X, MaxPoint.Y);
            }

            return key;
        }

        // 使用OpenCV模板匹配在屏幕中查找小图
        public async Task<Point> FindImageOnScreen(string imgName)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", imgName);
            if (!File.Exists(templatePath))
            {
                throw new Exception("图片不存在");
            }
            using (Mat source = Cv2.ImRead(screenshotPath))
            using (Mat template = Cv2.ImRead(templatePath))
            using (Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1))
            {
               
                // 检查模板图片是否成功加载
                if (template.Empty())
                {
                    throw new Exception($"无法加载模板图片: {templatePath}");
                }

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点
                Point centerPoint = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );
                result.Release();
                source.Release();
                template.Release();
                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    return new Point(-1, -1);
                }

                return centerPoint;
            }
        }

        // 关闭连接
        public void Close()
        {
            if (ws != null)
            {
                ws.CloseAsync(WebSocketCloseStatus.NormalClosure, "关闭连接", CancellationToken.None).Wait();
                ws = null;
            }
        }
    }

    public class ColorInfo
    {
        public byte R { get; set; }
        public byte G { get; set; }
        public byte B { get; set; }
        public byte A { get; set; }
        public string Hex { get; set; }
    }
}