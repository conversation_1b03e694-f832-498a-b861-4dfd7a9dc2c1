using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using OpenCvSharp;
using FFmpeg.AutoGen;
using Point = OpenCvSharp.Point;

namespace auto
{
    public class DeviceController
    {
        private string deviceId;
        public int width = 0;
        public int height = 0;

        private string screenshotPath = "";
        private Dictionary<string, Mat> matchs;
        private float threshold = 0.7f;
        private string adbPath;
        private string scrcpyServerPath;
        private Process scrcpyServerProcess;
        private TcpClient scrcpyClient;
        private NetworkStream scrcpyStream;
        private TcpClient scrcpyControlClient;
        private NetworkStream scrcpyControlStream;

        // FFmpeg相关
        private unsafe AVFormatContext* formatContext;
        private unsafe AVCodecContext* codecContext;
        private unsafe AVFrame* frame;
        private unsafe AVFrame* frameRGB;
        private unsafe SwsContext* swsContext;
        private int videoStreamIndex = -1;
        private bool isFFmpegInitialized = false;
        private byte[] frameBuffer;

        // 视频解码线程
        private Thread videoDecodeThread;
        private bool isDecoding = false;
        private readonly object frameLock = new object();
        private Bitmap latestFrame;
        public DeviceController()
        {
            matchs = new Dictionary<string, Mat>();

            // 设置adb路径为应用程序目录下的adb.exe
            adbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "adb.exe");

            // 设置scrcpy-server路径
            scrcpyServerPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "scrcpy-server.jar");

            var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "threshold.ini");
            if (File.Exists(path))
            {
                threshold = float.Parse(File.ReadAllText(path));
            }
            Console.WriteLine($"本次运行置信度 {threshold}");

            // 检查adb是否存在
            if (!File.Exists(adbPath))
            {
                throw new Exception($"未找到adb.exe文件，请将adb.exe放置在应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
            }

            // 检查scrcpy-server是否存在
            if (!File.Exists(scrcpyServerPath))
            {
                throw new Exception($"未找到scrcpy-server.jar文件，请将scrcpy-server.jar放置在应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
            }

            // 初始化FFmpeg
            InitializeFFmpeg();
        }

        // 初始化FFmpeg
        private unsafe void InitializeFFmpeg()
        {
            try
            {
                // 设置FFmpeg库路径
                string ffmpegPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ffmpeg");
                if (Directory.Exists(ffmpegPath))
                {
                    ffmpeg.RootPath = ffmpegPath;
                }

                // 注册所有编解码器
                ffmpeg.av_register_all();
                ffmpeg.avformat_network_init();

                Console.WriteLine($"FFmpeg初始化成功，版本: {ffmpeg.av_version_info()}");
                isFFmpegInitialized = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"FFmpeg初始化失败: {ex.Message}");
                Console.WriteLine("请确保FFmpeg库文件在应用程序目录的ffmpeg文件夹中");
                isFFmpegInitialized = false;
            }
        }

        // 执行adb命令
        private async Task<string> ExecuteAdbCommand(string arguments)
        {
            try
            {
                using (var process = new Process())
                {
                    process.StartInfo.FileName = adbPath;
                    process.StartInfo.Arguments = arguments;
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.StandardOutputEncoding = Encoding.UTF8;

                    process.Start();

                    string output = await process.StandardOutput.ReadToEndAsync();
                    string error = await process.StandardError.ReadToEndAsync();

                    await Task.Run(() => process.WaitForExit());

                    if (process.ExitCode != 0 && !string.IsNullOrEmpty(error))
                    {
                        throw new Exception($"ADB命令执行失败: {error}");
                    }

                    return output.Trim();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"执行ADB命令失败: {ex.Message}");
            }
        }

        // 初始化连接（替代原来的Connect方法）
        public async Task Connect()
        {
            try
            {
                // 启动adb服务器
                await ExecuteAdbCommand("start-server");
                Console.WriteLine("ADB服务器已启动");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动ADB服务器失败: {ex.Message}");
                throw;
            }
        }

        // 启动scrcpy-server视频流服务
        private async Task StartScrcpyServer()
        {
            try
            {
                if (string.IsNullOrEmpty(deviceId))
                {
                    throw new Exception("设备ID未设置，请先获取设备列表");
                }

                Console.WriteLine("正在启动scrcpy-server视频流服务...");

                // 推送scrcpy-server到设备
                await ExecuteAdbCommand($"-s {deviceId} push \"{scrcpyServerPath}\" /data/local/tmp/scrcpy-server.jar");

                // 验证文件是否推送成功
                string checkResult = await ExecuteAdbCommand($"-s {deviceId} shell ls -la /data/local/tmp/scrcpy-server.jar");
                if (checkResult.Contains("No such file"))
                {
                    throw new Exception("scrcpy-server.jar推送失败");
                }

                // 设置端口转发 - 视频流
                await ExecuteAdbCommand($"-s {deviceId} forward tcp:27183 localabstract:scrcpy");

                // 设置端口转发 - 控制流
                await ExecuteAdbCommand($"-s {deviceId} forward tcp:27184 localabstract:scrcpy_control");

                // 启动scrcpy-server进程（视频流模式）
                scrcpyServerProcess = new Process();
                scrcpyServerProcess.StartInfo.FileName = adbPath;
                scrcpyServerProcess.StartInfo.Arguments = $"-s {deviceId} shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server 2.1.1 info tunnel_forward=true control=true cleanup=true";
                scrcpyServerProcess.StartInfo.UseShellExecute = false;
                scrcpyServerProcess.StartInfo.CreateNoWindow = true;
                scrcpyServerProcess.StartInfo.RedirectStandardOutput = true;
                scrcpyServerProcess.StartInfo.RedirectStandardError = true;

                scrcpyServerProcess.Start();

                Console.WriteLine("scrcpy-server进程已启动，等待连接...");

                // 等待服务器启动
                await Task.Delay(3000);

                // 连接到scrcpy-server
                await ConnectToScrcpyServer();

                // 初始化视频解码器
                await InitializeVideoDecoder();

                // 启动视频解码线程
                StartVideoDecoding();

                Console.WriteLine("scrcpy视频流服务启动成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动scrcpy-server失败: {ex.Message}");
                throw;
            }
        }

        // 连接到scrcpy-server
        private async Task ConnectToScrcpyServer()
        {
            try
            {
                Console.WriteLine("连接到scrcpy视频流...");

                // 连接视频流
                scrcpyClient = new TcpClient();
                scrcpyClient.ReceiveTimeout = 10000;
                scrcpyClient.SendTimeout = 10000;
                await scrcpyClient.ConnectAsync("127.0.0.1", 27183);
                scrcpyStream = scrcpyClient.GetStream();

                // 读取设备信息（scrcpy协议要求）
                byte[] deviceInfoBuffer = new byte[69];
                int bytesRead = await scrcpyStream.ReadAsync(deviceInfoBuffer, 0, deviceInfoBuffer.Length);
                Console.WriteLine($"读取设备信息: {bytesRead} 字节");

                // 连接控制流
                Console.WriteLine("连接到scrcpy控制流...");
                scrcpyControlClient = new TcpClient();
                scrcpyControlClient.ReceiveTimeout = 10000;
                scrcpyControlClient.SendTimeout = 10000;
                await scrcpyControlClient.ConnectAsync("127.0.0.1", 27184);
                scrcpyControlStream = scrcpyControlClient.GetStream();

                Console.WriteLine("scrcpy连接成功");
            }
            catch (Exception ex)
            {
                throw new Exception($"连接scrcpy失败: {ex.Message}");
            }
        }

        // 初始化视频解码器
        private unsafe Task InitializeVideoDecoder()
        {
            return Task.Run(() =>
            {
                try
                {
                    if (!isFFmpegInitialized)
                    {
                        throw new Exception("FFmpeg未初始化");
                    }

                    Console.WriteLine("初始化视频解码器...");

                    // 分配格式上下文
                    formatContext = ffmpeg.avformat_alloc_context();
                    if (formatContext == null)
                    {
                        throw new Exception("无法分配格式上下文");
                    }

                    // 分配帧
                    frame = ffmpeg.av_frame_alloc();
                    frameRGB = ffmpeg.av_frame_alloc();

                    if (frame == null || frameRGB == null)
                    {
                        throw new Exception("无法分配帧");
                    }

                    Console.WriteLine("视频解码器初始化成功");
                }
                catch (Exception ex)
                {
                    throw new Exception($"初始化视频解码器失败: {ex.Message}");
                }
            });
        }

        // 启动视频解码线程
        private void StartVideoDecoding()
        {
            if (videoDecodeThread != null && videoDecodeThread.IsAlive)
            {
                return;
            }

            isDecoding = true;
            videoDecodeThread = new Thread(VideoDecodeLoop)
            {
                IsBackground = true,
                Name = "VideoDecodeThread"
            };
            videoDecodeThread.Start();

            Console.WriteLine("视频解码线程已启动");
        }

        // 视频解码循环
        private unsafe void VideoDecodeLoop()
        {
            try
            {
                Console.WriteLine("开始视频解码循环...");

                while (isDecoding && scrcpyStream != null)
                {
                    try
                    {
                        // 读取视频帧头部
                        byte[] frameHeader = new byte[12];
                        int headerBytesRead = 0;

                        while (headerBytesRead < frameHeader.Length && isDecoding)
                        {
                            int bytesRead = scrcpyStream.Read(frameHeader, headerBytesRead, frameHeader.Length - headerBytesRead);
                            if (bytesRead == 0)
                            {
                                Console.WriteLine("视频流结束");
                                return;
                            }
                            headerBytesRead += bytesRead;
                        }

                        // 解析帧大小 (big-endian)
                        uint frameSize = (uint)((frameHeader[8] << 24) | (frameHeader[9] << 16) | (frameHeader[10] << 8) | frameHeader[11]);

                        if (frameSize > 0 && frameSize < 10 * 1024 * 1024) // 限制最大10MB
                        {
                            // 读取视频帧数据
                            byte[] frameData = new byte[frameSize];
                            int frameBytesRead = 0;

                            while (frameBytesRead < frameSize && isDecoding)
                            {
                                int bytesRead = scrcpyStream.Read(frameData, frameBytesRead, (int)(frameSize - frameBytesRead));
                                if (bytesRead == 0)
                                {
                                    Console.WriteLine("视频流中断");
                                    return;
                                }
                                frameBytesRead += bytesRead;
                            }

                            // 解码帧
                            DecodeFrame(frameData);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解码帧时出错: {ex.Message}");
                        Thread.Sleep(100); // 短暂等待后继续
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"视频解码循环出错: {ex.Message}");
            }
            finally
            {
                Console.WriteLine("视频解码循环结束");
            }
        }

        // 解码单个帧
        private unsafe void DecodeFrame(byte[] frameData)
        {
            try
            {
                // 这里需要实现H.264解码
                // 由于复杂性，我们先实现一个简化版本
                // 实际项目中需要完整的H.264解码实现

                // 暂时跳过复杂的解码，直接生成一个测试图像
                // 在实际实现中，这里应该是完整的FFmpeg H.264解码过程

                // 创建一个简单的测试图像作为占位符
                CreateTestFrame();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解码帧失败: {ex.Message}");
            }
        }

        // 创建测试帧（占位符方法）
        private void CreateTestFrame()
        {
            try
            {
                // 创建一个简单的测试图像
                var bitmap = new Bitmap(width > 0 ? width : 1080, height > 0 ? height : 1920, PixelFormat.Format24bppRgb);

                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.Clear(Color.Black);
                    graphics.DrawString($"Frame {DateTime.Now:HH:mm:ss.fff}",
                                      new Font("Arial", 20),
                                      Brushes.White,
                                      new PointF(50, 50));
                }

                lock (frameLock)
                {
                    latestFrame?.Dispose();
                    latestFrame = bitmap;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建测试帧失败: {ex.Message}");
            }
        }

        // 测试scrcpy是否可用
        private async Task TestScrcpyAvailability()
        {
            try
            {
                Console.WriteLine("测试scrcpy可用性...");

                // 尝试运行一个简单的scrcpy命令来测试
                string testCommand = $"-s {deviceId} shell \"CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture --help 2>&1 || echo 'test_complete'\"";
                string testOutput = await ExecuteAdbCommand(testCommand);

                Console.WriteLine($"scrcpy测试输出: {testOutput?.Substring(0, Math.Min(100, testOutput?.Length ?? 0))}");

                // 如果包含错误信息，说明scrcpy不可用
                if (testOutput.Contains("ClassNotFoundException") ||
                    testOutput.Contains("NoClassDefFoundError") ||
                    testOutput.Contains("FATAL") ||
                    testOutput.Contains("Aborted"))
                {
                    throw new Exception($"scrcpy不兼容此设备: {testOutput}");
                }

                Console.WriteLine("scrcpy可用性测试通过");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"scrcpy可用性测试失败: {ex.Message}");
                throw;
            }
        }

        // 获取设备列表
        public async Task<string> GetDeviceList()
        {
            try
            {
                string output = await ExecuteAdbCommand("devices");
                string[] lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                foreach (string line in lines)
                {
                    if (line.Contains("\tdevice") && !line.StartsWith("List of devices"))
                    {
                        deviceId = line.Split('\t')[0];
                        Console.WriteLine($"找到设备: {deviceId}");

                        // 设备找到后，启动scrcpy-server
                        await StartScrcpyServer();

                        return deviceId;
                    }
                }

                throw new Exception("未找到已连接的设备，请确保设备已连接并启用USB调试");
            }
            catch (Exception ex)
            {
                throw new Exception($"获取设备列表失败: {ex.Message}");
            }
        }

        // 截图 - 从视频流获取最新帧
        public async Task<string> TakeScreenshot()
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                string screenshotDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "screenshots");
                if (!Directory.Exists(screenshotDir))
                {
                    Directory.CreateDirectory(screenshotDir);
                }

                screenshotPath = Path.Combine(screenshotDir, $"{deviceId}.png");

                // 从视频流获取最新帧
                Bitmap currentFrame = null;
                lock (frameLock)
                {
                    if (latestFrame != null)
                    {
                        currentFrame = new Bitmap(latestFrame);
                    }
                }

                if (currentFrame == null)
                {
                    throw new Exception("没有可用的视频帧");
                }

                // 保存帧为PNG文件
                currentFrame.Save(screenshotPath, ImageFormat.Png);
                currentFrame.Dispose();

                Console.WriteLine($"截图成功，文件大小: {new FileInfo(screenshotPath).Length} 字节");
                return screenshotPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"截图失败: {ex.Message}");
            }
        }





        public async Task GetDeviceInfo()
        {
            try
            {
                // 首先尝试通过adb命令获取屏幕分辨率
                string sizeOutput = await ExecuteAdbCommand($"-s {deviceId} shell wm size");

                // 解析输出，格式通常是 "Physical size: 1080x2340"
                if (sizeOutput.Contains("x"))
                {
                    string[] parts = sizeOutput.Split(':');
                    if (parts.Length > 1)
                    {
                        string[] dimensions = parts[1].Trim().Split('x');
                        if (dimensions.Length == 2)
                        {
                            width = int.Parse(dimensions[0]);
                            height = int.Parse(dimensions[1]);
                            Console.WriteLine($"设备分辨率：{width} x {height}");
                            return;
                        }
                    }
                }

                // 如果adb命令获取失败，则通过截图获取分辨率
                Console.WriteLine("通过adb获取分辨率失败，尝试通过截图获取...");
                string filename = await TakeScreenshot();

                using (var bitmap = new Bitmap(filename))
                {
                    width = bitmap.Width;
                    height = bitmap.Height;
                    Console.WriteLine($"设备分辨率：{width} x {height}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取设备信息失败: {ex.Message}");
            }
        }

        // 模拟点击
        public async Task Tap(float x, float y)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                // 如果坐标是相对坐标（0-1之间），转换为绝对坐标
                if (x <= 1)
                {
                    x *= width;
                }
                if (y <= 1)
                {
                    y *= height;
                }

                // 使用scrcpy控制进行点击
                await ScrcpyTap((int)x, (int)y);
            }
            catch (Exception ex)
            {
                throw new Exception($"点击失败: {ex.Message}");
            }
        }

        // 模拟滑动
        public async Task Swipe(float startX, float startY, float endX, float endY)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            try
            {
                // 如果坐标是相对坐标（0-1之间），转换为绝对坐标
                if (startX <= 1)
                {
                    startX *= width;
                }
                if (startY <= 1)
                {
                    startY *= height;
                }
                if (endX <= 1)
                {
                    endX *= width;
                }
                if (endY <= 1)
                {
                    endY *= height;
                }

                // 使用scrcpy控制进行滑动
                await ScrcpySwipe((int)startX, (int)startY, (int)endX, (int)endY);
            }
            catch (Exception ex)
            {
                throw new Exception($"滑动失败: {ex.Message}");
            }
        }

        public async Task<string> MatchImagesAndTap(string[] list)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }
            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }

            Mat source1 = Cv2.ImRead(screenshotPath);

            // 检查图像是否成功加载
            if (source1.Empty())
            {
                throw new Exception("无法加载截图文件");
            }

            // 将截图缩放到标准尺寸 1080x488
            const int targetWidth = 1080;
            const int targetHeight = 488;
            Mat source = new Mat();
            Cv2.Resize(source1, source, new OpenCvSharp.Size(targetWidth, targetHeight));
            source1.Release();
            Point MaxPoint = new Point();
            float maxT = 0;
            string key = "";
            foreach (var item in list)
            {
                if (!matchs.TryGetValue(item, out Mat template))
                {
                    string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", $"{item}.png");
                    if (!File.Exists(templatePath))
                    {
                        throw new Exception("图片不存在");
                    }
                    template = Cv2.ImRead(templatePath);
                    matchs[item] = template;
                }
                if (template.Empty())
                {
                    continue;
                }
                Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1);

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点（在1080x488缩放图像中的坐标）
                Point centerPoint = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );
                result.Release();
                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    continue;
                }
                if (confidence > maxT)
                {
                    maxT = (float)confidence;
                    MaxPoint = centerPoint;
                    key = item;
                    if (key == "fa")
                    {
                        break;
                    }
                }
            }
            source.Release();
            if (maxT > 0)
            {
                // 将1080x488坐标转换为实际设备坐标
                float actualX = (float)MaxPoint.X * width / targetWidth;
                float actualY = (float)MaxPoint.Y * height / targetHeight;
                await Tap(actualX, actualY);
            }

            return key;
        }

        // 使用OpenCV模板匹配在屏幕中查找小图
        public async Task<Point> FindImageOnScreen(string imgName)
        {
            if (string.IsNullOrEmpty(deviceId))
            {
                throw new Exception("未选择设备");
            }

            if (string.IsNullOrEmpty(screenshotPath))
            {
                throw new Exception("获取截图失败");
            }
            string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "min", imgName);
            if (!File.Exists(templatePath))
            {
                throw new Exception("图片不存在");
            }
            Mat sourceOriginal = Cv2.ImRead(screenshotPath);
            if (sourceOriginal.Empty())
            {
                throw new Exception("无法加载截图文件");
            }

            // 将截图缩放到标准尺寸 1080x488
            const int targetWidth = 1080;
            const int targetHeight = 488;
            Mat source = new Mat();
            Cv2.Resize(sourceOriginal, source, new OpenCvSharp.Size(targetWidth, targetHeight));
            sourceOriginal.Release();

            using (Mat template = Cv2.ImRead(templatePath))
            using (Mat result = new Mat(source.Rows - template.Rows + 1, source.Cols - template.Cols + 1, MatType.CV_32FC1))
            {
                // 检查模板图片是否成功加载
                if (template.Empty())
                {
                    throw new Exception($"无法加载模板图片: {templatePath}");
                }

                // 执行模板匹配
                Cv2.MatchTemplate(source, template, result, TemplateMatchModes.CCoeffNormed);

                // 查找最佳匹配位置
                Cv2.MinMaxLoc(result, out double minVal, out double maxVal, out Point minLoc, out Point maxLoc);

                // 使用归一化相关系数方法，最大值表示最佳匹配
                double confidence = maxVal;
                Point matchLocation = maxLoc;

                // 计算匹配图像的中心点（在1080x488缩放图像中的坐标）
                Point centerPointScaled = new Point(
                    matchLocation.X + template.Width / 2,
                    matchLocation.Y + template.Height / 2
                );

                result.Release();
                source.Release();
                template.Release();

                // 如果置信度低于阈值，认为未找到
                if (confidence < threshold)
                {
                    return new Point(-1, -1);
                }

                // 将1080x488坐标转换为实际设备坐标
                Point actualCenterPoint = new Point(
                    (int)(centerPointScaled.X * width / (float)targetWidth),
                    (int)(centerPointScaled.Y * height / (float)targetHeight)
                );

                return actualCenterPoint;
            }
        }

        // scrcpy点击控制
        private async Task ScrcpyTap(int x, int y)
        {
            try
            {
                // scrcpy触摸事件协议
                // 按下事件
                await SendScrcpyTouchEvent(0, x, y, 1.0f, 0); // ACTION_DOWN
                await Task.Delay(10); // 短暂延迟
                // 抬起事件
                await SendScrcpyTouchEvent(1, x, y, 1.0f, 0); // ACTION_UP
            }
            catch (Exception ex)
            {
                throw new Exception($"scrcpy点击失败: {ex.Message}");
            }
        }

        // scrcpy滑动控制
        private async Task ScrcpySwipe(int startX, int startY, int endX, int endY)
        {
            try
            {
                // 计算滑动步数
                int steps = 20;
                float deltaX = (float)(endX - startX) / steps;
                float deltaY = (float)(endY - startY) / steps;

                // 按下事件
                await SendScrcpyTouchEvent(0, startX, startY, 1.0f, 0); // ACTION_DOWN
                await Task.Delay(10);

                // 移动事件
                for (int i = 1; i <= steps; i++)
                {
                    int currentX = startX + (int)(deltaX * i);
                    int currentY = startY + (int)(deltaY * i);
                    await SendScrcpyTouchEvent(2, currentX, currentY, 1.0f, 0); // ACTION_MOVE
                    await Task.Delay(10);
                }

                // 抬起事件
                await SendScrcpyTouchEvent(1, endX, endY, 1.0f, 0); // ACTION_UP
            }
            catch (Exception ex)
            {
                throw new Exception($"scrcpy滑动失败: {ex.Message}");
            }
        }

        // 发送scrcpy触摸事件
        private async Task SendScrcpyTouchEvent(int action, int x, int y, float pressure, long pointerId)
        {
            try
            {
                // scrcpy控制消息格式
                byte[] message = new byte[28];

                // 消息类型 (1 byte) - INJECT_TOUCH_EVENT = 2
                message[0] = 2;

                // 动作 (1 byte)
                message[1] = (byte)action;

                // 指针ID (8 bytes, big-endian)
                byte[] pointerIdBytes = BitConverter.GetBytes(pointerId);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(pointerIdBytes);
                Array.Copy(pointerIdBytes, 0, message, 2, 8);

                // X坐标 (4 bytes, big-endian)
                byte[] xBytes = BitConverter.GetBytes(x);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(xBytes);
                Array.Copy(xBytes, 0, message, 10, 4);

                // Y坐标 (4 bytes, big-endian)
                byte[] yBytes = BitConverter.GetBytes(y);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(yBytes);
                Array.Copy(yBytes, 0, message, 14, 4);

                // 屏幕宽度 (2 bytes, big-endian)
                byte[] widthBytes = BitConverter.GetBytes((ushort)width);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(widthBytes);
                Array.Copy(widthBytes, 0, message, 18, 2);

                // 屏幕高度 (2 bytes, big-endian)
                byte[] heightBytes = BitConverter.GetBytes((ushort)height);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(heightBytes);
                Array.Copy(heightBytes, 0, message, 20, 2);

                // 压力值 (2 bytes, big-endian) - 转换为0-65535范围
                ushort pressureValue = (ushort)(pressure * 65535);
                byte[] pressureBytes = BitConverter.GetBytes(pressureValue);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(pressureBytes);
                Array.Copy(pressureBytes, 0, message, 22, 2);

                // 按钮状态 (4 bytes, big-endian)
                byte[] buttonBytes = BitConverter.GetBytes(0);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(buttonBytes);
                Array.Copy(buttonBytes, 0, message, 24, 4);

                // 发送消息
                await scrcpyControlStream.WriteAsync(message, 0, message.Length);
                await scrcpyControlStream.FlushAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"发送scrcpy触摸事件失败: {ex.Message}");
            }
        }

        // scrcpy息屏功能
        public async Task TurnOffScreen()
        {
            try
            {
                if (useScrcpyForControl && scrcpyControlStream != null)
                {
                    // 使用scrcpy控制协议发送息屏命令
                    await SendScrcpyPowerEvent(0); // POWER_MODE_OFF
                    Console.WriteLine("已通过scrcpy发送息屏命令");
                }
                else
                {
                    // 回退到adb命令
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_POWER");
                    Console.WriteLine("已通过adb发送息屏命令");
                }
            }
            catch (Exception ex)
            {
                // 如果scrcpy控制失败，尝试回退到adb
                if (useScrcpyForControl)
                {
                    Console.WriteLine($"scrcpy息屏失败，回退到adb方式: {ex.Message}");
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_POWER");
                }
                else
                {
                    throw new Exception($"息屏失败: {ex.Message}");
                }
            }
        }

        // scrcpy亮屏功能
        public async Task TurnOnScreen()
        {
            try
            {
                if (useScrcpyForControl && scrcpyControlStream != null)
                {
                    // 使用scrcpy控制协议发送亮屏命令
                    await SendScrcpyPowerEvent(2); // POWER_MODE_NORMAL
                    Console.WriteLine("已通过scrcpy发送亮屏命令");
                }
                else
                {
                    // 回退到adb命令
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_WAKEUP");
                    Console.WriteLine("已通过adb发送亮屏命令");
                }
            }
            catch (Exception ex)
            {
                // 如果scrcpy控制失败，尝试回退到adb
                if (useScrcpyForControl)
                {
                    Console.WriteLine($"scrcpy亮屏失败，回退到adb方式: {ex.Message}");
                    await ExecuteAdbCommand($"-s {deviceId} shell input keyevent KEYCODE_WAKEUP");
                }
                else
                {
                    throw new Exception($"亮屏失败: {ex.Message}");
                }
            }
        }

        // 发送scrcpy电源事件
        private async Task SendScrcpyPowerEvent(int powerMode)
        {
            try
            {
                // scrcpy电源控制消息格式
                byte[] message = new byte[2];

                // 消息类型 (1 byte) - SET_SCREEN_POWER_MODE = 10
                message[0] = 10;

                // 电源模式 (1 byte)
                // 0 = POWER_MODE_OFF (息屏)
                // 2 = POWER_MODE_NORMAL (正常/亮屏)
                message[1] = (byte)powerMode;

                // 发送消息
                await scrcpyControlStream.WriteAsync(message, 0, message.Length);
                await scrcpyControlStream.FlushAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"发送scrcpy电源事件失败: {ex.Message}");
            }
        }

        // 切换屏幕状态（息屏/亮屏）
        public async Task ToggleScreen()
        {
            try
            {
                bool isScreenOn = await IsScreenOn();
                if (isScreenOn)
                {
                    await TurnOffScreen();
                    Console.WriteLine("屏幕已息屏");
                }
                else
                {
                    await TurnOnScreen();
                    Console.WriteLine("屏幕已亮屏");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"切换屏幕状态失败: {ex.Message}");
            }
        }

        // 检查屏幕是否亮屏
        public async Task<bool> IsScreenOn()
        {
            try
            {
                // 使用adb命令检查屏幕状态
                string output = await ExecuteAdbCommand($"-s {deviceId} shell dumpsys power | grep \"mHoldingDisplaySuspendBlocker\"");

                // 如果包含"mHoldingDisplaySuspendBlocker=true"，说明屏幕是亮的
                bool isOn = output.Contains("mHoldingDisplaySuspendBlocker=true");

                // 如果上面的方法不工作，尝试另一种方法
                if (string.IsNullOrEmpty(output))
                {
                    output = await ExecuteAdbCommand($"-s {deviceId} shell dumpsys power | grep \"Display Power\"");
                    isOn = output.Contains("state=ON") || output.Contains("state=2");
                }

                return isOn;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"检查屏幕状态失败: {ex.Message}");
                // 如果检查失败，默认假设屏幕是亮的
                return true;
            }
        }

        // 息屏并等待指定时间后自动亮屏
        public async Task TurnOffScreenTemporarily(int seconds)
        {
            try
            {
                Console.WriteLine($"息屏 {seconds} 秒...");
                await TurnOffScreen();

                // 等待指定时间
                await Task.Delay(seconds * 1000);

                await TurnOnScreen();
                Console.WriteLine("自动亮屏完成");
            }
            catch (Exception ex)
            {
                throw new Exception($"临时息屏失败: {ex.Message}");
            }
        }

        // 关闭连接
        public unsafe void Close()
        {
            try
            {
                // 停止视频解码
                isDecoding = false;
                if (videoDecodeThread != null && videoDecodeThread.IsAlive)
                {
                    videoDecodeThread.Join(2000); // 等待最多2秒
                }

                // 清理FFmpeg资源
                if (swsContext != null)
                {
                    ffmpeg.sws_freeContext(swsContext);
                    swsContext = null;
                }

                if (frame != null)
                {
                    ffmpeg.av_frame_free(&frame);
                    frame = null;
                }

                if (frameRGB != null)
                {
                    ffmpeg.av_frame_free(&frameRGB);
                    frameRGB = null;
                }

                if (codecContext != null)
                {
                    ffmpeg.avcodec_free_context(&codecContext);
                    codecContext = null;
                }

                if (formatContext != null)
                {
                    ffmpeg.avformat_close_input(&formatContext);
                    formatContext = null;
                }

                // 清理最新帧
                lock (frameLock)
                {
                    latestFrame?.Dispose();
                    latestFrame = null;
                }

                // 清理scrcpy相关资源
                if (scrcpyStream != null)
                {
                    scrcpyStream.Close();
                    scrcpyStream = null;
                }

                if (scrcpyClient != null)
                {
                    scrcpyClient.Close();
                    scrcpyClient = null;
                }

                if (scrcpyControlStream != null)
                {
                    scrcpyControlStream.Close();
                    scrcpyControlStream = null;
                }

                if (scrcpyControlClient != null)
                {
                    scrcpyControlClient.Close();
                    scrcpyControlClient = null;
                }

                if (scrcpyServerProcess != null && !scrcpyServerProcess.HasExited)
                {
                    scrcpyServerProcess.Kill();
                    scrcpyServerProcess = null;
                }

                // 清理端口转发
                if (!string.IsNullOrEmpty(deviceId))
                {
                    try
                    {
                        ExecuteAdbCommand($"-s {deviceId} forward --remove tcp:27183").Wait();
                        ExecuteAdbCommand($"-s {deviceId} forward --remove tcp:27184").Wait();
                    }
                    catch
                    {
                        // 忽略清理错误
                    }
                }

                // 释放图像匹配缓存
                if (matchs != null)
                {
                    foreach (var mat in matchs.Values)
                    {
                        mat?.Release();
                    }
                    matchs.Clear();
                }

                Console.WriteLine("所有资源已清理");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理资源时出错: {ex.Message}");
            }
        }
    }

    public class ColorInfo
    {
        public byte R { get; set; }
        public byte G { get; set; }
        public byte B { get; set; }
        public byte A { get; set; }
        public string Hex { get; set; }
    }
}