@echo off
chcp 65001 >nul
echo ========================================
echo    ADB连接测试工具
echo ========================================
echo.

REM 检查adb.exe是否存在
if not exist "adb.exe" (
    echo ❌ 错误: 未找到adb.exe文件
    echo.
    echo 请确保adb.exe文件在当前目录中
    echo 当前目录: %~dp0
    echo.
    echo 如需下载ADB工具，请运行 download_adb.bat
    echo.
    pause
    exit /b 1
)

echo ✅ 找到adb.exe文件
echo.

echo 正在启动ADB服务器...
adb start-server
if %errorlevel% neq 0 (
    echo ❌ ADB服务器启动失败
    pause
    exit /b 1
)
echo ✅ ADB服务器启动成功
echo.

echo 正在检测连接的设备...
adb devices
echo.

echo 获取设备数量...
for /f "skip=1 tokens=1" %%i in ('adb devices ^| findstr "device$"') do (
    set device_found=1
    set device_id=%%i
    goto device_found
)

echo ❌ 未找到已连接的设备
echo.
echo 请检查：
echo 1. 设备是否通过USB连接到电脑
echo 2. 设备是否已启用USB调试
echo 3. 设备是否已授权此电脑进行调试
echo.
echo 启用USB调试的步骤：
echo 1. 进入设备的"设置" ^> "关于手机"
echo 2. 连续点击"版本号"7次启用开发者选项
echo 3. 进入"设置" ^> "开发者选项"
echo 4. 开启"USB调试"
echo.
pause
exit /b 1

:device_found
echo ✅ 找到设备: %device_id%
echo.

echo 正在获取设备信息...
echo 设备型号:
adb -s %device_id% shell getprop ro.product.model
echo.
echo 设备分辨率:
adb -s %device_id% shell wm size
echo.
echo Android版本:
adb -s %device_id% shell getprop ro.build.version.release
echo.

echo 正在测试截图功能...
adb -s %device_id% shell screencap -p /sdcard/test_screenshot.png
if %errorlevel% neq 0 (
    echo ❌ 截图测试失败
    pause
    exit /b 1
)

adb -s %device_id% pull /sdcard/test_screenshot.png test_screenshot.png
if %errorlevel% neq 0 (
    echo ❌ 截图下载失败
    pause
    exit /b 1
)

adb -s %device_id% shell rm /sdcard/test_screenshot.png

if exist "test_screenshot.png" (
    echo ✅ 截图测试成功 - 已保存为 test_screenshot.png
    del test_screenshot.png
) else (
    echo ❌ 截图文件未生成
    pause
    exit /b 1
)

echo.
echo ========================================
echo    测试完成
echo ========================================
echo.
echo ✅ 所有测试通过！
echo.
echo 设备已准备就绪，可以运行auto.exe开始自动化操作
echo.
pause
