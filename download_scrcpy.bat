@echo off
chcp 65001 >nul
echo ========================================
echo    scrcpy-server下载助手
echo ========================================
echo.
echo 本脚本将帮助您下载scrcpy-server.jar文件
echo.
echo scrcpy-server.jar用于实现高速截图和控制功能：
echo - 截图速度提升10-20倍（从800-1500ms降低到50-200ms）
echo - 控制延迟降低5-10倍（从100-300ms降低到10-50ms）
echo - 显著提升自动化脚本性能和响应速度
echo.
echo 请选择下载方式：
echo 1. 打开scrcpy官方下载页面（推荐）
echo 2. 显示手动下载说明
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" goto official
if "%choice%"=="2" goto manual
if "%choice%"=="3" goto exit
goto invalid

:official
echo.
echo 正在打开scrcpy官方下载页面...
start https://github.com/Genymobile/scrcpy/releases
echo.
echo 下载完成后：
echo 1. 下载最新版本的scrcpy-win64.zip或scrcpy-win32.zip
echo 2. 解压下载的zip文件
echo 3. 在解压目录中找到scrcpy-server.jar文件
echo 4. 将scrcpy-server.jar复制到当前目录: %~dp0
echo.
echo 注意：
echo - 选择与您系统匹配的版本（64位或32位）
echo - scrcpy-server.jar通常在解压后的根目录中
echo - 文件大小约为40-50KB
echo.
pause
goto exit

:manual
echo.
echo ========================================
echo    手动下载说明
echo ========================================
echo.
echo 方法1 - 从scrcpy官方releases下载:
echo 1. 访问: https://github.com/Genymobile/scrcpy/releases
echo 2. 下载最新版本的scrcpy-win64.zip（推荐）或scrcpy-win32.zip
echo 3. 解压zip文件
echo 4. 复制scrcpy-server.jar到当前目录
echo.
echo 方法2 - 如果您已安装scrcpy:
echo 1. 找到scrcpy的安装目录
echo 2. 在安装目录中找到scrcpy-server.jar
echo 3. 复制到当前目录
echo.
echo 方法3 - 从源码编译（高级用户）:
echo 1. 克隆scrcpy仓库
echo 2. 按照官方文档编译
echo 3. 在编译输出中找到scrcpy-server.jar
echo.
echo 当前目录: %~dp0
echo.
echo 完成后，scrcpy-server.jar应该与auto.exe在同一目录中
echo.
echo 验证方法：
echo - 文件名: scrcpy-server.jar
echo - 文件大小: 约40-50KB
echo - 文件类型: Java Archive (JAR)
echo.
pause
goto exit

:invalid
echo.
echo 无效选择，请重新运行脚本
pause
goto exit

:exit
echo.
echo ========================================
echo    重要提示
echo ========================================
echo.
echo scrcpy-server.jar是可选文件：
echo.
echo ✅ 有scrcpy-server.jar:
echo   - 截图速度: 50-200ms
echo   - 控制延迟: 10-50ms
echo   - 性能: 优秀
echo   - 适合: 高频操作场景
echo.
echo ⚠️  没有scrcpy-server.jar:
echo   - 截图速度: 800-1500ms
echo   - 控制延迟: 100-300ms
echo   - 性能: 一般
echo   - 适合: 低频操作场景
echo   - 程序会自动回退到adb方式
echo.
echo 推荐下载scrcpy-server.jar以获得最佳性能和响应速度！
echo.
echo 感谢使用！
pause
