@echo off
chcp 65001 >nul
echo ========================================
echo    FFmpeg视频解码测试工具
echo ========================================
echo.

REM 检查必需文件
if not exist "adb.exe" (
    echo ❌ 错误: 未找到adb.exe文件
    pause
    exit /b 1
)

if not exist "scrcpy-server.jar" (
    echo ❌ 错误: 未找到scrcpy-server.jar文件
    pause
    exit /b 1
)

if not exist "ffmpeg" (
    echo ❌ 错误: 未找到ffmpeg目录
    echo 请创建ffmpeg目录并放置FFmpeg库文件
    pause
    exit /b 1
)

echo ✅ 找到必需文件
echo.

echo 正在启动ADB服务器...
adb start-server

echo 正在检测设备...
for /f "skip=1 tokens=1" %%i in ('adb devices ^| findstr "device$"') do (
    set device_found=1
    set device_id=%%i
    goto device_found
)

echo ❌ 未找到已连接的设备
pause
exit /b 1

:device_found
echo ✅ 找到设备: %device_id%
echo.

echo ========================================
echo    步骤1: 检查FFmpeg库文件
echo ========================================
echo.

set ffmpeg_ok=1

if not exist "ffmpeg\avcodec*.dll" (
    echo ❌ 未找到avcodec库文件
    set ffmpeg_ok=0
)

if not exist "ffmpeg\avformat*.dll" (
    echo ❌ 未找到avformat库文件
    set ffmpeg_ok=0
)

if not exist "ffmpeg\avutil*.dll" (
    echo ❌ 未找到avutil库文件
    set ffmpeg_ok=0
)

if not exist "ffmpeg\swscale*.dll" (
    echo ❌ 未找到swscale库文件
    set ffmpeg_ok=0
)

if not exist "ffmpeg\swresample*.dll" (
    echo ❌ 未找到swresample库文件
    set ffmpeg_ok=0
)

if %ffmpeg_ok%==1 (
    echo ✅ FFmpeg库文件检查通过
    echo.
    echo 找到的库文件:
    dir /b ffmpeg\*.dll
) else (
    echo ❌ FFmpeg库文件不完整
    echo.
    echo 请确保ffmpeg目录包含以下文件:
    echo - avcodec-xx.dll
    echo - avformat-xx.dll
    echo - avutil-xx.dll
    echo - swscale-xx.dll
    echo - swresample-xx.dll
    echo.
    pause
    exit /b 1
)

echo.

echo ========================================
echo    步骤2: 推送scrcpy-server
echo ========================================
echo.

echo 推送scrcpy-server.jar到设备...
adb -s %device_id% push scrcpy-server.jar /data/local/tmp/scrcpy-server.jar
if %errorlevel% neq 0 (
    echo ❌ 推送失败
    pause
    exit /b 1
)

echo 验证文件是否存在...
adb -s %device_id% shell ls -la /data/local/tmp/scrcpy-server.jar
echo.

echo ========================================
echo    步骤3: 设置端口转发
echo ========================================
echo.

echo 设置视频流端口转发...
adb -s %device_id% forward tcp:27183 localabstract:scrcpy
if %errorlevel% neq 0 (
    echo ❌ 视频流端口转发失败
    pause
    exit /b 1
)

echo 设置控制流端口转发...
adb -s %device_id% forward tcp:27184 localabstract:scrcpy_control
if %errorlevel% neq 0 (
    echo ❌ 控制流端口转发失败
    pause
    exit /b 1
)

echo ✅ 端口转发设置成功
echo.

echo ========================================
echo    步骤4: 启动scrcpy-server
echo ========================================
echo.

echo 启动scrcpy-server进程...
echo 命令: adb -s %device_id% shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server 2.1.1 info tunnel_forward=true control=true cleanup=true
echo.

start /b adb -s %device_id% shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.Server 2.1.1 info tunnel_forward=true control=true cleanup=true

echo 等待scrcpy-server启动...
timeout /t 5 /nobreak >nul

echo ========================================
echo    步骤5: 测试连接
echo ========================================
echo.

echo 测试视频流连接...
powershell -Command "try { $client = New-Object System.Net.Sockets.TcpClient; $client.Connect('127.0.0.1', 27183); $client.Close(); Write-Host '✅ 视频流连接成功' } catch { Write-Host '❌ 视频流连接失败' }"

echo.
echo 测试控制流连接...
powershell -Command "try { $client = New-Object System.Net.Sockets.TcpClient; $client.Connect('127.0.0.1', 27184); $client.Close(); Write-Host '✅ 控制流连接成功' } catch { Write-Host '❌ 控制流连接失败' }"

echo.

echo ========================================
echo    步骤6: 运行主程序测试
echo ========================================
echo.

echo 现在可以运行auto.exe测试FFmpeg视频解码功能
echo.
echo 预期行为:
echo 1. 程序启动后会初始化FFmpeg
echo 2. 连接到scrcpy视频流
echo 3. 开始解码视频帧
echo 4. 截图将直接从视频帧获取（毫秒级）
echo.

echo 性能指标:
echo - 视频流延迟: 10-30ms
echo - 解码延迟: 1-5ms  
echo - 截图延迟: <1ms
echo.

echo 如果遇到问题:
echo 1. 检查FFmpeg库文件是否完整
echo 2. 确认scrcpy-server版本兼容性
echo 3. 查看程序输出的错误信息
echo.

echo 按任意键启动auto.exe进行测试...
pause >nul

echo 启动auto.exe...
start auto.exe

echo.
echo 测试完成！
echo 如果程序正常运行，说明FFmpeg视频解码功能工作正常。
echo.
pause
