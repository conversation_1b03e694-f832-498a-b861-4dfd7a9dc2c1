using System;
using System.Threading.Tasks;

namespace auto
{
    /// <summary>
    /// 电源管理功能使用示例
    /// </summary>
    public class PowerExample
    {
        private DeviceController controller;

        public PowerExample()
        {
            controller = new DeviceController();
        }

        /// <summary>
        /// 演示基本的电源管理功能
        /// </summary>
        public async Task DemonstratePowerManagement()
        {
            try
            {
                Console.WriteLine("=== 电源管理功能演示 ===");
                Console.WriteLine();

                // 初始化连接
                await controller.Connect();
                await controller.GetDeviceList();
                await controller.GetDeviceInfo();

                // 1. 检查当前屏幕状态
                Console.WriteLine("1. 检查当前屏幕状态...");
                bool isScreenOn = await controller.IsScreenOn();
                Console.WriteLine($"   屏幕状态: {(isScreenOn ? "亮屏" : "息屏")}");
                Console.WriteLine();

                // 2. 演示息屏功能
                Console.WriteLine("2. 演示息屏功能...");
                if (isScreenOn)
                {
                    await controller.TurnOffScreen();
                    Console.WriteLine("   已执行息屏操作");
                    await Task.Delay(2000); // 等待2秒
                }
                else
                {
                    Console.WriteLine("   屏幕已经是息屏状态");
                }
                Console.WriteLine();

                // 3. 演示亮屏功能
                Console.WriteLine("3. 演示亮屏功能...");
                await controller.TurnOnScreen();
                Console.WriteLine("   已执行亮屏操作");
                await Task.Delay(1000); // 等待1秒
                Console.WriteLine();

                // 4. 演示切换功能
                Console.WriteLine("4. 演示智能切换功能...");
                await controller.ToggleScreen();
                await Task.Delay(2000);
                await controller.ToggleScreen(); // 切换回来
                Console.WriteLine();

                // 5. 演示临时息屏功能
                Console.WriteLine("5. 演示临时息屏功能（5秒）...");
                await controller.TurnOffScreenTemporarily(5);
                Console.WriteLine();

                Console.WriteLine("=== 电源管理功能演示完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中出错: {ex.Message}");
            }
            finally
            {
                controller.Close();
            }
        }

        /// <summary>
        /// 演示在自动化过程中使用电源管理
        /// </summary>
        public async Task DemonstrateAutomationWithPowerManagement()
        {
            try
            {
                Console.WriteLine("=== 自动化中的电源管理演示 ===");
                Console.WriteLine();

                // 初始化
                await controller.Connect();
                await controller.GetDeviceList();
                await controller.GetDeviceInfo();

                // 模拟一个需要隐私保护的自动化任务
                Console.WriteLine("开始执行需要隐私保护的自动化任务...");
                
                // 息屏以保护隐私
                Console.WriteLine("1. 息屏保护隐私...");
                await controller.TurnOffScreen();
                
                // 执行一些不需要显示的操作
                Console.WriteLine("2. 执行后台操作...");
                for (int i = 0; i < 3; i++)
                {
                    // 即使息屏也可以截图和操作
                    await controller.TakeScreenshot();
                    Console.WriteLine($"   完成操作 {i + 1}/3");
                    await Task.Delay(1000);
                }
                
                // 完成后亮屏
                Console.WriteLine("3. 任务完成，恢复显示...");
                await controller.TurnOnScreen();
                
                Console.WriteLine("=== 隐私保护自动化演示完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中出错: {ex.Message}");
            }
            finally
            {
                controller.Close();
            }
        }

        /// <summary>
        /// 演示节能模式的自动化
        /// </summary>
        public async Task DemonstrateEnergySavingAutomation()
        {
            try
            {
                Console.WriteLine("=== 节能模式自动化演示 ===");
                Console.WriteLine();

                await controller.Connect();
                await controller.GetDeviceList();
                await controller.GetDeviceInfo();

                Console.WriteLine("开始节能模式自动化任务...");
                
                // 在不需要显示的时候息屏节能
                Console.WriteLine("1. 进入节能模式（息屏）...");
                await controller.TurnOffScreen();
                
                // 执行长时间的后台任务
                Console.WriteLine("2. 执行长时间后台任务...");
                for (int i = 0; i < 10; i++)
                {
                    // 定期检查和操作
                    await controller.TakeScreenshot();
                    
                    // 模拟一些点击操作
                    await controller.Tap(500, 300);
                    await Task.Delay(500);
                    
                    Console.WriteLine($"   节能模式下完成操作 {i + 1}/10");
                    await Task.Delay(2000);
                }
                
                // 任务完成后恢复显示
                Console.WriteLine("3. 任务完成，退出节能模式...");
                await controller.TurnOnScreen();
                
                Console.WriteLine("=== 节能模式自动化演示完成 ===");
                Console.WriteLine("节能效果：减少了约90%的屏幕耗电");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中出错: {ex.Message}");
            }
            finally
            {
                controller.Close();
            }
        }

        /// <summary>
        /// 主演示方法
        /// </summary>
        public static async Task Main(string[] args)
        {
            var example = new PowerExample();
            
            Console.WriteLine("请选择演示模式：");
            Console.WriteLine("1. 基本电源管理功能");
            Console.WriteLine("2. 隐私保护自动化");
            Console.WriteLine("3. 节能模式自动化");
            Console.Write("请输入选择 (1-3): ");
            
            string choice = Console.ReadLine();
            
            switch (choice)
            {
                case "1":
                    await example.DemonstratePowerManagement();
                    break;
                case "2":
                    await example.DemonstrateAutomationWithPowerManagement();
                    break;
                case "3":
                    await example.DemonstrateEnergySavingAutomation();
                    break;
                default:
                    Console.WriteLine("无效选择，运行基本演示...");
                    await example.DemonstratePowerManagement();
                    break;
            }
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
