@echo off
chcp 65001 >nul
echo ========================================
echo    scrcpy修复版本测试工具
echo ========================================
echo.

REM 检查必需文件
if not exist "adb.exe" (
    echo ❌ 错误: 未找到adb.exe文件
    pause
    exit /b 1
)

echo ✅ 找到adb.exe文件

if not exist "scrcpy-server.jar" (
    echo ⚠️  警告: 未找到scrcpy-server.jar文件
    echo    将仅使用adb方式进行测试
    set use_scrcpy=0
) else (
    echo ✅ 找到scrcpy-server.jar文件
    set use_scrcpy=1
)

echo.

echo 正在启动ADB服务器...
adb start-server
if %errorlevel% neq 0 (
    echo ❌ ADB服务器启动失败
    pause
    exit /b 1
)

echo 正在检测设备...
for /f "skip=1 tokens=1" %%i in ('adb devices ^| findstr "device$"') do (
    set device_found=1
    set device_id=%%i
    goto device_found
)

echo ❌ 未找到已连接的设备
pause
exit /b 1

:device_found
echo ✅ 找到设备: %device_id%
echo.

if %use_scrcpy%==1 (
    echo ========================================
    echo    测试scrcpy截图功能
    echo ========================================
    echo.
    
    echo 1. 推送scrcpy-server到设备...
    adb -s %device_id% push scrcpy-server.jar /data/local/tmp/scrcpy-server.jar
    if %errorlevel% neq 0 (
        echo ❌ 推送scrcpy-server失败
        goto test_adb_only
    )
    echo ✅ scrcpy-server推送成功
    
    echo.
    echo 2. 测试scrcpy截图命令...
    
    REM 创建screenshots目录
    if not exist "screenshots" mkdir screenshots
    
    echo 正在执行scrcpy截图命令...
    echo 命令: adb -s %device_id% shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture
    
    adb -s %device_id% shell CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture > screenshots\scrcpy_output.txt
    
    if exist "screenshots\scrcpy_output.txt" (
        echo ✅ scrcpy命令执行完成
        echo.
        echo 检查输出文件...
        for %%A in (screenshots\scrcpy_output.txt) do set size=%%~zA
        echo 输出文件大小: !size! 字节
        
        if !size! gtr 100 (
            echo ✅ scrcpy截图可能成功（输出大小正常）
            echo.
            echo 输出内容预览（前100字符）:
            powershell -Command "Get-Content screenshots\scrcpy_output.txt | Select-Object -First 1 | ForEach-Object { $_.Substring(0, [Math]::Min(100, $_.Length)) }"
        ) else (
            echo ❌ scrcpy截图可能失败（输出太小）
            echo.
            echo 完整输出内容:
            type screenshots\scrcpy_output.txt
        )
        
        del screenshots\scrcpy_output.txt
    ) else (
        echo ❌ scrcpy命令执行失败，无输出文件
    )
    
    echo.
    echo 3. 测试设备信息获取...
    echo 设备型号:
    adb -s %device_id% shell getprop ro.product.model
    echo.
    echo 设备分辨率:
    adb -s %device_id% shell wm size
    echo.
    echo Android版本:
    adb -s %device_id% shell getprop ro.build.version.release
    echo.
    
    goto test_complete
)

:test_adb_only
echo.
echo ========================================
echo    ADB截图测试
echo ========================================
echo.
echo 正在测试adb截图功能...

REM 创建screenshots目录
if not exist "screenshots" mkdir screenshots

echo 正在进行adb截图测试...
adb -s %device_id% shell screencap -p /sdcard/adb_test.png
adb -s %device_id% pull /sdcard/adb_test.png screenshots\adb_test.png
adb -s %device_id% shell rm /sdcard/adb_test.png

if exist "screenshots\adb_test.png" (
    echo ✅ adb截图测试成功！
    echo    截图已保存为: screenshots\adb_test.png
    for %%A in (screenshots\adb_test.png) do echo    文件大小: %%~zA 字节
    del screenshots\adb_test.png
) else (
    echo ❌ adb截图测试失败
)

:test_complete
echo.
echo ========================================
echo    测试总结
echo ========================================
echo.

if %use_scrcpy%==1 (
    echo scrcpy测试结果：
    echo - scrcpy-server.jar: ✅ 已找到
    echo - 推送到设备: ✅ 成功
    echo - 截图命令: 请查看上面的输出结果
    echo.
    echo 如果scrcpy截图失败，可能的原因：
    echo 1. scrcpy-server.jar版本不兼容
    echo 2. 设备Android版本不支持
    echo 3. 权限问题
    echo.
    echo 解决方案：
    echo 1. 下载最新版本的scrcpy-server.jar
    echo 2. 确保设备已root或使用兼容版本
    echo 3. 程序会自动回退到adb截图方式
) else (
    echo scrcpy测试结果：
    echo - scrcpy-server.jar: ❌ 未找到
    echo - 将使用adb截图方式
)

echo.
echo adb测试结果：
echo - adb连接: ✅ 正常
echo - 设备检测: ✅ 正常
echo - 截图功能: 请查看上面的测试结果

echo.
echo 现在可以运行auto.exe测试完整功能
echo 程序会根据scrcpy可用性自动选择最佳截图方式
echo.
pause
