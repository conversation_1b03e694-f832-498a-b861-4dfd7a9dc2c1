using OpenCvSharp.Flann;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace auto
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            var controller = new DeviceController();

            Dictionary<string,string> matchs = new Dictionary<string, string>()
            {
                {"cf","大厅出发" },
                {"fhdt","返回大厅" },
                {"fq","放弃" },
                {"lk","离开" },
                {"qr","确认" },
                {"xyb","下一步" },
                {"fa","方案" },
                {"gm","购买" },
                {"qrpz","确认配装" },
                {"qwpz","前往配装" },
                {"tg","跳过大厅" }
            };
            
            try
            {
                // 初始化ADB连接
                await controller.Connect();

                // 获取设备列表
                string deviceId = await controller.GetDeviceList();
                Console.WriteLine($"设备名称: {deviceId}");

                // 截图
                await controller.GetDeviceInfo();
                int index = 0;
                Console.Title = $"已运行局数{index}";
                while (true) {
                   var ret = await controller.TakeScreenshot();
                    if (string.IsNullOrWhiteSpace(ret))
                    {
                        Console.WriteLine("截图失败,等待重试");
                        Thread.Sleep(2000);
                        continue;
                    }
                    var pos = await controller.FindImageOnScreen("c4.png");
                    if (pos.X > 0 && pos.Y > 0)
                    {
                        Console.WriteLine("正在使用磁吸炸弹");
                        await controller.Swipe(0.7f, 0.5f, 0.7f, 0.9f);
                        Thread.Sleep(20);
                        await controller.Swipe(0.7f, 0.5f, 0.7f, 0.9f);
                        Thread.Sleep(20);
                        await controller.Swipe(0.7f, 0.5f, 0.7f, 0.9f);
                        Thread.Sleep(20);
                        await controller.Tap(pos.X, pos.Y);
                        Thread.Sleep(1500);
                        await controller.Tap(pos.X, pos.Y);
                        Thread.Sleep(3500);
                        index++;
                        Console.Title = $"已运行局数{index}";
                        continue;
                    }
                    var key = await controller.MatchImagesAndTap(matchs.Keys.ToArray());
                    if (key != "")
                    {
                        Console.WriteLine($"执行操作：{matchs[key]}");
                    }
                    Thread.Sleep(1000);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"操作失败: {ex.Message}");
            }
            finally
            {
                // 清理资源
                controller.Close();
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
