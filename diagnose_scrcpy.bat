@echo off
chcp 65001 >nul
echo ========================================
echo    scrcpy问题诊断工具
echo ========================================
echo.

REM 检查必需文件
if not exist "adb.exe" (
    echo ❌ 错误: 未找到adb.exe文件
    pause
    exit /b 1
)

if not exist "scrcpy-server.jar" (
    echo ❌ 错误: 未找到scrcpy-server.jar文件
    pause
    exit /b 1
)

echo ✅ 找到必需文件
echo.

echo 正在启动ADB服务器...
adb start-server

echo 正在检测设备...
for /f "skip=1 tokens=1" %%i in ('adb devices ^| findstr "device$"') do (
    set device_found=1
    set device_id=%%i
    goto device_found
)

echo ❌ 未找到已连接的设备
pause
exit /b 1

:device_found
echo ✅ 找到设备: %device_id%
echo.

echo ========================================
echo    步骤1: 检查设备基本信息
echo ========================================
echo.
echo 设备型号:
adb -s %device_id% shell getprop ro.product.model
echo.
echo Android版本:
adb -s %device_id% shell getprop ro.build.version.release
echo.
echo API级别:
adb -s %device_id% shell getprop ro.build.version.sdk
echo.

echo ========================================
echo    步骤2: 推送scrcpy-server
echo ========================================
echo.
echo 推送scrcpy-server.jar到设备...
adb -s %device_id% push scrcpy-server.jar /data/local/tmp/scrcpy-server.jar
if %errorlevel% neq 0 (
    echo ❌ 推送失败
    pause
    exit /b 1
)

echo 验证文件是否存在...
adb -s %device_id% shell ls -la /data/local/tmp/scrcpy-server.jar
echo.

echo ========================================
echo    步骤3: 测试Java环境
echo ========================================
echo.
echo 检查Java是否可用...
adb -s %device_id% shell "which app_process"
echo.
echo 测试基本Java命令...
adb -s %device_id% shell "app_process --version 2>&1 || echo 'app_process available'"
echo.

echo ========================================
echo    步骤4: 测试scrcpy类加载
echo ========================================
echo.
echo 测试scrcpy类是否可以加载...
adb -s %device_id% shell "CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture --help 2>&1"
echo.

echo ========================================
echo    步骤5: 尝试不同的调用方式
echo ========================================
echo.

echo 方式1: 标准调用
echo 命令: CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture
adb -s %device_id% shell "timeout 5 CLASSPATH=/data/local/tmp/scrcpy-server.jar app_process / com.genymobile.scrcpy.wrappers.ScreenCapture 2>&1 | head -10"
echo.

echo 方式2: 简化路径调用
echo 命令: cd /data/local/tmp && CLASSPATH=scrcpy-server.jar app_process . com.genymobile.scrcpy.wrappers.ScreenCapture
adb -s %device_id% shell "cd /data/local/tmp && timeout 5 CLASSPATH=scrcpy-server.jar app_process . com.genymobile.scrcpy.wrappers.ScreenCapture 2>&1 | head -10"
echo.

echo 方式3: 直接调用
echo 命令: cd /data/local/tmp && app_process -Djava.class.path=scrcpy-server.jar . com.genymobile.scrcpy.wrappers.ScreenCapture
adb -s %device_id% shell "cd /data/local/tmp && timeout 5 app_process -Djava.class.path=scrcpy-server.jar . com.genymobile.scrcpy.wrappers.ScreenCapture 2>&1 | head -10"
echo.

echo ========================================
echo    步骤6: 检查权限和环境
echo ========================================
echo.
echo 检查/data/local/tmp权限...
adb -s %device_id% shell "ls -ld /data/local/tmp"
echo.
echo 检查scrcpy-server.jar权限...
adb -s %device_id% shell "ls -la /data/local/tmp/scrcpy-server.jar"
echo.
echo 检查可执行权限...
adb -s %device_id% shell "which java || echo 'Java not found in PATH'"
echo.

echo ========================================
echo    步骤7: 尝试传统截图对比
echo ========================================
echo.
echo 测试传统adb截图...
adb -s %device_id% shell screencap -p /sdcard/test_adb.png
adb -s %device_id% pull /sdcard/test_adb.png test_adb.png 2>nul
adb -s %device_id% shell rm /sdcard/test_adb.png

if exist "test_adb.png" (
    echo ✅ 传统adb截图成功
    for %%A in (test_adb.png) do echo    文件大小: %%~zA 字节
    del test_adb.png
) else (
    echo ❌ 传统adb截图也失败
)

echo.
echo ========================================
echo    诊断总结
echo ========================================
echo.
echo 请查看上面的输出信息来诊断问题：
echo.
echo 常见问题和解决方案：
echo.
echo 1. 如果看到 "ClassNotFoundException":
echo    - scrcpy-server.jar版本可能不兼容
echo    - 尝试下载最新版本的scrcpy-server.jar
echo.
echo 2. 如果看到 "Permission denied":
echo    - 设备可能需要root权限
echo    - 或者/data/local/tmp目录权限问题
echo.
echo 3. 如果看到 "Aborted" 或 "FATAL":
echo    - Android版本可能不支持
echo    - 尝试使用较旧版本的scrcpy-server.jar
echo.
echo 4. 如果所有方式都失败:
echo    - 程序会自动回退到adb截图方式
echo    - 虽然速度较慢但功能完整
echo.
echo 建议：
echo - 如果scrcpy不工作，可以继续使用程序
echo - 程序会自动使用adb方式，功能不受影响
echo - 只是截图速度会稍慢一些
echo.
pause
