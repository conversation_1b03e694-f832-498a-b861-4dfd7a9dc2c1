@echo off
chcp 65001 >nul
echo ========================================
echo    ADB工具下载助手
echo ========================================
echo.
echo 本脚本将帮助您下载ADB工具到当前目录
echo.
echo 请选择下载方式：
echo 1. 打开官方下载页面（推荐）
echo 2. 显示手动下载说明
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" goto official
if "%choice%"=="2" goto manual
if "%choice%"=="3" goto exit
goto invalid

:official
echo.
echo 正在打开Android官方Platform Tools下载页面...
start https://developer.android.com/studio/releases/platform-tools
echo.
echo 下载完成后：
echo 1. 解压下载的zip文件
echo 2. 在platform-tools目录中找到adb.exe
echo 3. 将adb.exe复制到当前目录: %~dp0
echo.
pause
goto exit

:manual
echo.
echo ========================================
echo    手动下载说明
echo ========================================
echo.
echo 方法1 - 官方Android SDK Platform Tools:
echo 1. 访问: https://developer.android.com/studio/releases/platform-tools
echo 2. 下载适用于Windows的platform-tools
echo 3. 解压zip文件
echo 4. 复制platform-tools\adb.exe到当前目录
echo.
echo 方法2 - 第三方最小化ADB工具:
echo 1. 搜索"minimal adb fastboot"
echo 2. 下载适用于Windows的版本
echo 3. 复制adb.exe到当前目录
echo.
echo 当前目录: %~dp0
echo.
echo 完成后，adb.exe应该与auto.exe在同一目录中
echo.
pause
goto exit

:invalid
echo.
echo 无效选择，请重新运行脚本
pause
goto exit

:exit
echo.
echo 感谢使用！
pause
