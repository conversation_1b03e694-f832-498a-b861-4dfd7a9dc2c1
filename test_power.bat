@echo off
chcp 65001 >nul
echo ========================================
echo    scrcpy电源管理测试工具
echo ========================================
echo.

REM 检查必需文件
if not exist "adb.exe" (
    echo ❌ 错误: 未找到adb.exe文件
    pause
    exit /b 1
)

echo ✅ 找到adb.exe文件
echo.

echo 正在启动ADB服务器...
adb start-server
if %errorlevel% neq 0 (
    echo ❌ ADB服务器启动失败
    pause
    exit /b 1
)

echo 正在检测设备...
for /f "skip=1 tokens=1" %%i in ('adb devices ^| findstr "device$"') do (
    set device_found=1
    set device_id=%%i
    goto device_found
)

echo ❌ 未找到已连接的设备
pause
exit /b 1

:device_found
echo ✅ 找到设备: %device_id%
echo.

echo ========================================
echo    电源管理功能测试
echo ========================================
echo.

echo 当前屏幕状态检测...
adb -s %device_id% shell dumpsys power | findstr "mHoldingDisplaySuspendBlocker"
echo.

echo 请选择要测试的功能：
echo 1. 息屏测试
echo 2. 亮屏测试
echo 3. 屏幕状态检测
echo 4. 临时息屏测试（5秒）
echo 5. 切换屏幕状态
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto turn_off
if "%choice%"=="2" goto turn_on
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto temp_off
if "%choice%"=="5" goto toggle
if "%choice%"=="6" goto exit
goto invalid

:turn_off
echo.
echo 正在执行息屏操作...
if exist "scrcpy-server.jar" (
    echo 使用scrcpy方式息屏...
    echo 注意：需要应用程序运行scrcpy-server才能使用scrcpy协议
    echo 当前使用adb方式作为演示...
)
adb -s %device_id% shell input keyevent KEYCODE_POWER
echo ✅ 息屏命令已发送
echo.
goto menu

:turn_on
echo.
echo 正在执行亮屏操作...
if exist "scrcpy-server.jar" (
    echo 使用scrcpy方式亮屏...
    echo 注意：需要应用程序运行scrcpy-server才能使用scrcpy协议
    echo 当前使用adb方式作为演示...
)
adb -s %device_id% shell input keyevent KEYCODE_WAKEUP
echo ✅ 亮屏命令已发送
echo.
goto menu

:check_status
echo.
echo 正在检查屏幕状态...
echo.
echo 方法1: 检查DisplaySuspendBlocker
adb -s %device_id% shell dumpsys power | findstr "mHoldingDisplaySuspendBlocker"
echo.
echo 方法2: 检查Display Power状态
adb -s %device_id% shell dumpsys power | findstr "Display Power"
echo.
echo 方法3: 检查屏幕状态
adb -s %device_id% shell dumpsys power | findstr "state="
echo.
goto menu

:temp_off
echo.
echo 正在执行临时息屏测试（5秒）...
echo 1. 息屏...
adb -s %device_id% shell input keyevent KEYCODE_POWER
echo ✅ 已息屏，等待5秒...
timeout /t 5 /nobreak >nul
echo 2. 亮屏...
adb -s %device_id% shell input keyevent KEYCODE_WAKEUP
echo ✅ 临时息屏测试完成
echo.
goto menu

:toggle
echo.
echo 正在检测当前屏幕状态并切换...
for /f "tokens=*" %%a in ('adb -s %device_id% shell dumpsys power ^| findstr "mHoldingDisplaySuspendBlocker"') do set power_status=%%a

if "%power_status%" == "" (
    echo 无法检测屏幕状态，默认执行息屏操作
    adb -s %device_id% shell input keyevent KEYCODE_POWER
    echo ✅ 已执行息屏操作
) else (
    echo 当前状态: %power_status%
    echo %power_status% | findstr "true" >nul
    if !errorlevel! == 0 (
        echo 屏幕当前是亮的，执行息屏...
        adb -s %device_id% shell input keyevent KEYCODE_POWER
        echo ✅ 已息屏
    ) else (
        echo 屏幕当前是暗的，执行亮屏...
        adb -s %device_id% shell input keyevent KEYCODE_WAKEUP
        echo ✅ 已亮屏
    )
)
echo.
goto menu

:menu
echo 是否继续测试其他功能？
echo 1. 返回主菜单
echo 2. 退出
set /p continue_choice=请选择 (1-2): 
if "%continue_choice%"=="1" goto device_found
goto exit

:invalid
echo.
echo 无效选择，请重新选择
goto device_found

:exit
echo.
echo ========================================
echo    测试说明
echo ========================================
echo.
echo 电源管理功能说明：
echo.
echo ✅ scrcpy方式（推荐）:
echo   - 延迟更低
echo   - 控制更精确
echo   - 需要scrcpy-server.jar和应用程序运行
echo.
echo ✅ adb方式（备用）:
echo   - 兼容性好
echo   - 无需额外文件
echo   - 本测试脚本使用的方式
echo.
echo 在实际应用中，程序会自动选择最佳方式
echo.
echo 感谢使用！
pause
